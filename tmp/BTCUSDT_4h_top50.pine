//@version=5
indicator("Multi-Rectangle List with Labels", overlay=true, max_boxes_count=500)

var int[] start_times = array.from(
  timestamp("UTC", 2025, 1, 16, 20, 0), timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 20, 0),
  timestamp("UTC", 2025, 7, 11, 0, 0), timestamp("UTC", 2025, 7, 11, 4, 0), timestamp("UTC", 2025, 7, 12, 4, 0),
  timestamp("UTC", 2025, 1, 17, 0, 0), timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 18, 4, 0),
  timestamp("UTC", 2025, 1, 19, 4, 0), timestamp("UTC", 2025, 2, 4, 8, 0), timestamp("UTC", 2025, 2, 4, 12, 0),
  timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 5, 8, 0),
  timestamp("UTC", 2025, 2, 6, 0, 0), timestamp("UTC", 2025, 7, 10, 20, 0), timestamp("UTC", 2025, 7, 11, 0, 0),
  timestamp("UTC", 2025, 7, 11, 12, 0), timestamp("UTC", 2025, 7, 12, 12, 0), timestamp("UTC", 2025, 7, 12, 16, 0),
  timestamp("UTC", 2025, 7, 13, 8, 0), timestamp("UTC", 2025, 7, 13, 16, 0), timestamp("UTC", 2025, 7, 14, 8, 0),
  timestamp("UTC", 2025, 7, 14, 12, 0), timestamp("UTC", 2025, 7, 14, 16, 0), timestamp("UTC", 2024, 12, 17, 4, 0),
  timestamp("UTC", 2024, 12, 17, 20, 0), timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 20, 0),
  timestamp("UTC", 2024, 12, 19, 0, 0), timestamp("UTC", 2024, 12, 19, 4, 0), timestamp("UTC", 2025, 1, 16, 12, 0),
  timestamp("UTC", 2025, 1, 16, 16, 0), timestamp("UTC", 2025, 1, 17, 0, 0), timestamp("UTC", 2025, 1, 17, 0, 0),
  timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 20, 0),
  timestamp("UTC", 2025, 1, 18, 0, 0), timestamp("UTC", 2025, 1, 18, 12, 0), timestamp("UTC", 2025, 1, 19, 12, 0),
  timestamp("UTC", 2025, 2, 4, 16, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 5, 0, 0),
  timestamp("UTC", 2025, 2, 5, 16, 0), timestamp("UTC", 2025, 2, 5, 20, 0), timestamp("UTC", 2025, 2, 6, 4, 0),
  timestamp("UTC", 2025, 2, 26, 0, 0), timestamp("UTC", 2025, 7, 9, 0, 0)
  )
var int[] end_times = array.from(
  timestamp("UTC", 2025, 2, 1, 12, 0), timestamp("UTC", 2025, 7, 30, 20, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0),
  timestamp("UTC", 2025, 1, 30, 12, 0), timestamp("UTC", 2025, 1, 26, 8, 0), timestamp("UTC", 2025, 2, 1, 20, 0),
  timestamp("UTC", 2025, 2, 1, 8, 0), timestamp("UTC", 2025, 2, 17, 20, 0), timestamp("UTC", 2025, 2, 23, 12, 0),
  timestamp("UTC", 2025, 2, 17, 4, 0), timestamp("UTC", 2025, 2, 22, 16, 0), timestamp("UTC", 2025, 2, 18, 0, 0),
  timestamp("UTC", 2025, 2, 17, 8, 0), timestamp("UTC", 2025, 7, 28, 16, 0), timestamp("UTC", 2025, 7, 27, 20, 0),
  timestamp("UTC", 2025, 7, 29, 4, 0), timestamp("UTC", 2025, 7, 28, 4, 0), timestamp("UTC", 2025, 7, 30, 0, 0),
  timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 30, 20, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 31, 0, 0), timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 1, 15, 16, 0),
  timestamp("UTC", 2025, 1, 15, 12, 0), timestamp("UTC", 2025, 1, 15, 16, 0), timestamp("UTC", 2025, 1, 15, 12, 0),
  timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 16, 0), timestamp("UTC", 2025, 1, 27, 0, 0),
  timestamp("UTC", 2025, 1, 27, 0, 0), timestamp("UTC", 2025, 1, 27, 8, 0), timestamp("UTC", 2025, 1, 28, 12, 0),
  timestamp("UTC", 2025, 1, 25, 8, 0), timestamp("UTC", 2025, 1, 31, 12, 0), timestamp("UTC", 2025, 1, 26, 20, 0),
  timestamp("UTC", 2025, 1, 30, 0, 0), timestamp("UTC", 2025, 1, 30, 20, 0), timestamp("UTC", 2025, 1, 31, 4, 0),
  timestamp("UTC", 2025, 2, 15, 20, 0), timestamp("UTC", 2025, 2, 15, 0, 0), timestamp("UTC", 2025, 2, 16, 0, 0),
  timestamp("UTC", 2025, 2, 14, 16, 0), timestamp("UTC", 2025, 2, 15, 12, 0), timestamp("UTC", 2025, 2, 16, 8, 0),
  timestamp("UTC", 2025, 4, 21, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0)
  )
var float[] min_prices = array.from(
  97680.0, 111267.7, 113208.4,
  114667.3, 114239.0, 114667.3,
  97680.0, 99501.0, 97680.0,
  97680.0, 94003.9, 93321.6,
  94003.9, 93321.6, 94003.9,
  94003.9, 113208.4, 114667.3,
  114667.3, 114667.3, 114667.3,
  114239.0, 114667.3, 114667.3,
  114667.3, 114667.3, 88909.0,
  88909.0, 88909.0, 88909.0,
  88909.0, 88909.0, 97293.6,
  99023.9, 97680.0, 97680.0,
  99501.0, 97680.0, 99501.0,
  97680.0, 97680.0, 97680.0,
  94003.9, 94003.9, 94003.9,
  94003.9, 94003.9, 94003.9,
  74457.0, 108273.5
  )
var float[] max_prices = array.from(
  110000.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 100743.6, 100743.6,
  100112.4, 100112.4, 100112.4,
  100112.4, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 122772.7,
  122113.9, 120951.5, 108366.8,
  106877.3, 105343.8, 102954.8,
  104198.9, 105784.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  100743.6, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  94971.0, 123300.0
  )

// 硬编码标签文本
var string[] label_texts = array.from(
  "Box 0 (Score: 10.0)", "Box 1 (Score: 10.0)", "Box 2 (Score: 10.0)",
  "Box 3 (Score: 10.0)", "Box 4 (Score: 10.0)", "Box 5 (Score: 10.0)",
  "Box 6 (Score: 9.0)", "Box 7 (Score: 9.0)", "Box 8 (Score: 9.0)",
  "Box 9 (Score: 9.0)", "Box 10 (Score: 9.0)", "Box 11 (Score: 9.0)",
  "Box 12 (Score: 9.0)", "Box 13 (Score: 9.0)", "Box 14 (Score: 9.0)",
  "Box 15 (Score: 9.0)", "Box 16 (Score: 9.0)", "Box 17 (Score: 9.0)",
  "Box 18 (Score: 9.0)", "Box 19 (Score: 9.0)", "Box 20 (Score: 9.0)",
  "Box 21 (Score: 9.0)", "Box 22 (Score: 9.0)", "Box 23 (Score: 9.0)",
  "Box 24 (Score: 9.0)", "Box 25 (Score: 9.0)", "Box 26 (Score: 8.0)",
  "Box 27 (Score: 8.0)", "Box 28 (Score: 8.0)", "Box 29 (Score: 8.0)",
  "Box 30 (Score: 8.0)", "Box 31 (Score: 8.0)", "Box 32 (Score: 8.0)",
  "Box 33 (Score: 8.0)", "Box 34 (Score: 8.0)", "Box 35 (Score: 8.0)",
  "Box 36 (Score: 8.0)", "Box 37 (Score: 8.0)", "Box 38 (Score: 8.0)",
  "Box 39 (Score: 8.0)", "Box 40 (Score: 8.0)", "Box 41 (Score: 8.0)",
  "Box 42 (Score: 8.0)", "Box 43 (Score: 8.0)", "Box 44 (Score: 8.0)",
  "Box 45 (Score: 8.0)", "Box 46 (Score: 8.0)", "Box 47 (Score: 8.0)",
  "Box 48 (Score: 8.0)", "Box 49 (Score: 8.0)"
  )

var box[] rects = array.new_box()
var label[] labels = array.new_label()
var bool created = false

if not created
    for i = 0 to array.size(start_times) - 1
        // 创建矩形并存储引用
        box new_rect = box.new(
          left = array.get(start_times, i),
          top = array.get(max_prices,   i),
          right = array.get(end_times,   i),
          bottom = array.get(min_prices, i),
          border_color = color.green,
          border_width = 1,
          border_style = line.style_solid,
          extend = extend.none,
          xloc = xloc.bar_time,
          bgcolor = color.new(color.green, 80)
          )
        array.push(rects, new_rect)
        // 在矩形左上角创建标签并存储引用
        label new_lbl = label.new(
          x = array.get(start_times, i),
          y = array.get(max_prices,   i),
          text = array.get(label_texts, i),
          xloc = xloc.bar_time,
          yloc = yloc.price,
          style = label.style_label_left,
          color = color.green,
          textcolor = color.white
          )
        array.push(labels, new_lbl)
    created := true