//@version=5
indicator("Multi-Rectangle List with Labels", overlay=true, max_boxes_count=500)

var int[] start_times = array.from(
  timestamp("UTC", 2025, 1, 16, 20, 0), timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 20, 0),
  timestamp("UTC", 2025, 7, 11, 0, 0), timestamp("UTC", 2025, 7, 11, 4, 0), timestamp("UTC", 2025, 7, 12, 4, 0),
  timestamp("UTC", 2025, 1, 17, 0, 0), timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 18, 4, 0),
  timestamp("UTC", 2025, 1, 19, 4, 0)
  )
var int[] end_times = array.from(
  timestamp("UTC", 2025, 2, 1, 12, 0), timestamp("UTC", 2025, 7, 30, 20, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0),
  timestamp("UTC", 2025, 1, 30, 12, 0), timestamp("UTC", 2025, 1, 26, 8, 0), timestamp("UTC", 2025, 2, 1, 20, 0),
  timestamp("UTC", 2025, 2, 1, 8, 0)
  )
var float[] min_prices = array.from(
  97680.0, 111267.7, 113208.4,
  114667.3, 114239.0, 114667.3,
  97680.0, 99501.0, 97680.0,
  97680.0
  )
var float[] max_prices = array.from(
  110000.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  110000.0, 110000.0, 110000.0,
  110000.0
  )

// 硬编码标签文本
var string[] label_texts = array.from(
  "#0: 10.0分 (95根K线)", "#1: 10.0分 (122根K线)", "#2: 10.0分 (120根K线)",
  "#3: 10.0分 (119根K线)", "#4: 10.0分 (126根K线)", "#5: 10.0分 (118根K线)",
  "#6: 9.0分 (82根K线)", "#7: 9.0分 (55根K线)", "#8: 9.0分 (89根K线)",
  "#9: 9.0分 (80根K线)"
  )

var box[] rects = array.new_box()
var label[] labels = array.new_label()
var bool created = false

if not created
    for i = 0 to array.size(start_times) - 1
        // 创建矩形并存储引用
        box new_rect = box.new(
          left = array.get(start_times, i),
          top = array.get(max_prices,   i),
          right = array.get(end_times,   i),
          bottom = array.get(min_prices, i),
          border_color = color.green,
          border_width = 1,
          border_style = line.style_solid,
          extend = extend.none,
          xloc = xloc.bar_time,
          bgcolor = color.new(color.green, 80)
          )
        array.push(rects, new_rect)
        // 在矩形左上角创建标签并存储引用
        label new_lbl = label.new(
          x = array.get(start_times, i),
          y = array.get(max_prices,   i),
          text = array.get(label_texts, i),
          xloc = xloc.bar_time,
          yloc = yloc.price,
          style = label.style_label_left,
          color = color.green,
          textcolor = color.white
          )
        array.push(labels, new_lbl)
    created := true