#!/usr/bin/env python3
"""
详细箱体分析脚本
深入分析指定箱体区间内的K线数据
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime
import argparse


def load_kline_data(json_path: str) -> list:
    """加载K线数据"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    klines = []
    for item in data:
        klines.append({
            'timestamp': int(item[0]),
            'open': float(item[1]),
            'high': float(item[2]),
            'low': float(item[3]),
            'close': float(item[4]),
            'volume': float(item[5])
        })
    return klines


def analyze_box_detail(klines: list, left_idx: int, right_idx: int, box_id: int = 1):
    """详细分析箱体区间内的K线数据"""
    
    print(f"=== 箱体 {box_id} 详细分析 ===")
    print(f"索引范围: {left_idx} ~ {right_idx}")
    print(f"K线数量: {right_idx - left_idx + 1}")
    
    # 提取箱体内的K线
    box_klines = klines[left_idx:right_idx + 1]
    
    # 基本统计
    highs = [k['high'] for k in box_klines]
    lows = [k['low'] for k in box_klines]
    opens = [k['open'] for k in box_klines]
    closes = [k['close'] for k in box_klines]
    
    min_price = min(lows)
    max_price = max(highs)
    
    print(f"\n价格统计:")
    print(f"  最低价: {min_price}")
    print(f"  最高价: {max_price}")
    print(f"  价格范围: {max_price - min_price:.2f}")
    print(f"  开盘价: {opens[0]}")
    print(f"  收盘价: {closes[-1]}")
    print(f"  价格变化: {closes[-1] - opens[0]:.2f} ({(closes[-1] - opens[0])/opens[0]*100:.2f}%)")
    
    # 时间信息
    start_time = datetime.fromtimestamp(box_klines[0]['timestamp'] / 1000)
    end_time = datetime.fromtimestamp(box_klines[-1]['timestamp'] / 1000)
    duration = end_time - start_time
    
    print(f"\n时间信息:")
    print(f"  开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  持续时间: {duration.days}天 {duration.seconds//3600}小时")
    
    # 找出极值K线
    min_indices = [i for i, k in enumerate(box_klines) if k['low'] == min_price]
    max_indices = [i for i, k in enumerate(box_klines) if k['high'] == max_price]
    
    print(f"\n极值K线:")
    print(f"  最低价 {min_price} 出现次数: {len(min_indices)}")
    for i in min_indices[:5]:  # 只显示前5个
        kline = box_klines[i]
        time_str = datetime.fromtimestamp(kline['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
        print(f"    索引 {left_idx + i}: {time_str}, OHLC=[{kline['open']}, {kline['high']}, {kline['low']}, {kline['close']}]")
    
    print(f"  最高价 {max_price} 出现次数: {len(max_indices)}")
    for i in max_indices[:5]:  # 只显示前5个
        kline = box_klines[i]
        time_str = datetime.fromtimestamp(kline['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
        print(f"    索引 {left_idx + i}: {time_str}, OHLC=[{kline['open']}, {kline['high']}, {kline['low']}, {kline['close']}]")
    
    # 价格分布分析
    print(f"\n价格分布分析:")
    price_ranges = np.linspace(min_price, max_price, 11)
    for i in range(len(price_ranges) - 1):
        low_bound = price_ranges[i]
        high_bound = price_ranges[i + 1]
        count = sum(1 for k in box_klines if low_bound <= k['low'] <= high_bound or low_bound <= k['high'] <= high_bound)
        print(f"  {low_bound:.0f} - {high_bound:.0f}: {count} 根K线")
    
    # 检查前后几根K线的情况
    print(f"\n边界K线检查:")
    
    # 前面几根K线
    print(f"  箱体前的K线 (索引 {max(0, left_idx-3)} ~ {left_idx-1}):")
    for i in range(max(0, left_idx-3), left_idx):
        if i < len(klines):
            k = klines[i]
            time_str = datetime.fromtimestamp(k['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
            print(f"    索引 {i}: {time_str}, OHLC=[{k['open']}, {k['high']}, {k['low']}, {k['close']}]")
    
    # 后面几根K线
    print(f"  箱体后的K线 (索引 {right_idx+1} ~ {min(len(klines)-1, right_idx+3)}):")
    for i in range(right_idx+1, min(len(klines), right_idx+4)):
        k = klines[i]
        time_str = datetime.fromtimestamp(k['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
        print(f"    索引 {i}: {time_str}, OHLC=[{k['open']}, {k['high']}, {k['low']}, {k['close']}]")
    
    # 生成CSV文件用于进一步分析
    df_data = []
    for i, k in enumerate(box_klines):
        df_data.append({
            'global_idx': left_idx + i,
            'box_idx': i,
            'timestamp': k['timestamp'],
            'datetime': datetime.fromtimestamp(k['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            'open': k['open'],
            'high': k['high'],
            'low': k['low'],
            'close': k['close'],
            'volume': k['volume'],
            'is_min_price': k['low'] == min_price,
            'is_max_price': k['high'] == max_price
        })
    
    df = pd.DataFrame(df_data)
    csv_path = f'tmp/box_{box_id}_klines_detail.csv'
    df.to_csv(csv_path, index=False)
    print(f"\n详细K线数据已保存到: {csv_path}")
    
    return {
        'min_price': min_price,
        'max_price': max_price,
        'klines_count': len(box_klines),
        'min_occurrences': len(min_indices),
        'max_occurrences': len(max_indices)
    }


def main():
    parser = argparse.ArgumentParser(description='详细分析箱体K线数据')
    parser.add_argument('--klines', default='data/BTCUSDT_4h.json', help='K线数据文件路径')
    parser.add_argument('--left-idx', type=int, default=2062, help='左边界索引')
    parser.add_argument('--right-idx', type=int, default=2183, help='右边界索引')
    parser.add_argument('--box-id', type=int, default=1, help='箱体ID')
    
    args = parser.parse_args()
    
    print("Loading K-line data...")
    klines = load_kline_data(args.klines)
    print(f"Loaded {len(klines)} K-lines")
    
    # 验证索引范围
    if args.left_idx < 0 or args.right_idx >= len(klines) or args.left_idx > args.right_idx:
        print(f"Error: Invalid index range {args.left_idx} ~ {args.right_idx}")
        return
    
    # 执行详细分析
    result = analyze_box_detail(klines, args.left_idx, args.right_idx, args.box_id)
    
    print(f"\n=== 分析总结 ===")
    print(f"验证结果: 箱体边界数据准确")
    print(f"最低价 {result['min_price']} 在 {result['min_occurrences']} 根K线中出现")
    print(f"最高价 {result['max_price']} 在 {result['max_occurrences']} 根K线中出现")


if __name__ == "__main__":
    main()
