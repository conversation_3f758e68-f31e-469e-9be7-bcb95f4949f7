#!/usr/bin/env python3
"""
箱体数据验证脚本
验证NMS结果中的箱体边界数据是否与原始K线数据一致
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Tuple
import argparse


def load_kline_data(json_path: str) -> List[Dict]:
    """加载K线数据"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    klines = []
    for item in data:
        klines.append({
            'timestamp': int(item[0]),
            'open': float(item[1]),
            'high': float(item[2]),
            'low': float(item[3]),
            'close': float(item[4]),
            'volume': float(item[5])
        })
    return klines


def load_nms_results(json_path: str) -> Dict:
    """加载NMS结果"""
    with open(json_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def verify_box_boundaries(box_data: Dict, klines: List[Dict]) -> Dict:
    """验证单个箱体的边界数据"""
    boundaries = box_data['boundaries']
    left_idx = boundaries['left_idx']
    right_idx = boundaries['right_idx']
    reported_min = boundaries['min_price']
    reported_max = boundaries['max_price']
    
    # 检查索引范围
    if left_idx < 0 or right_idx >= len(klines) or left_idx > right_idx:
        return {
            'valid': False,
            'error': f"Invalid index range: left_idx={left_idx}, right_idx={right_idx}, klines_count={len(klines)}"
        }
    
    # 提取指定范围内的K线数据
    box_klines = klines[left_idx:right_idx + 1]
    
    # 计算实际的最高价和最低价
    actual_highs = [k['high'] for k in box_klines]
    actual_lows = [k['low'] for k in box_klines]
    actual_min = min(actual_lows)
    actual_max = max(actual_highs)
    
    # 计算差异
    min_diff = abs(actual_min - reported_min)
    max_diff = abs(actual_max - reported_max)
    min_diff_pct = (min_diff / actual_min * 100) if actual_min != 0 else 0
    max_diff_pct = (max_diff / actual_max * 100) if actual_max != 0 else 0
    
    # 获取时间信息
    start_timestamp = klines[left_idx]['timestamp']
    end_timestamp = klines[right_idx]['timestamp']
    start_time = datetime.fromtimestamp(start_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
    end_time = datetime.fromtimestamp(end_timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
    
    return {
        'valid': True,
        'box_id': box_data['box_id'],
        'indices': {
            'left_idx': left_idx,
            'right_idx': right_idx,
            'duration': right_idx - left_idx + 1
        },
        'time_range': {
            'start_time': start_time,
            'end_time': end_time,
            'start_timestamp': start_timestamp,
            'end_timestamp': end_timestamp
        },
        'price_comparison': {
            'reported_min': reported_min,
            'actual_min': actual_min,
            'min_diff': min_diff,
            'min_diff_pct': min_diff_pct,
            'reported_max': reported_max,
            'actual_max': actual_max,
            'max_diff': max_diff,
            'max_diff_pct': max_diff_pct
        },
        'klines_sample': {
            'first_kline': {
                'timestamp': box_klines[0]['timestamp'],
                'time': datetime.fromtimestamp(box_klines[0]['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                'ohlc': [box_klines[0]['open'], box_klines[0]['high'], box_klines[0]['low'], box_klines[0]['close']]
            },
            'last_kline': {
                'timestamp': box_klines[-1]['timestamp'],
                'time': datetime.fromtimestamp(box_klines[-1]['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                'ohlc': [box_klines[-1]['open'], box_klines[-1]['high'], box_klines[-1]['low'], box_klines[-1]['close']]
            },
            'extreme_prices': {
                'min_price_klines': [
                    {
                        'idx': left_idx + i,
                        'time': datetime.fromtimestamp(k['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'low': k['low']
                    }
                    for i, k in enumerate(box_klines) if k['low'] == actual_min
                ],
                'max_price_klines': [
                    {
                        'idx': left_idx + i,
                        'time': datetime.fromtimestamp(k['timestamp'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                        'high': k['high']
                    }
                    for i, k in enumerate(box_klines) if k['high'] == actual_max
                ]
            }
        }
    }


def main():
    parser = argparse.ArgumentParser(description='验证箱体数据准确性')
    parser.add_argument('--klines', default='data/BTCUSDT_4h.json', help='K线数据文件路径')
    parser.add_argument('--nms-results', default='results/BTCUSDT_4h_nms.json', help='NMS结果文件路径')
    parser.add_argument('--box-id', type=int, help='指定要验证的箱体ID（可选，不指定则验证所有）')
    parser.add_argument('--tolerance', type=float, default=0.01, help='价格差异容忍度百分比（默认1%）')
    parser.add_argument('--output', help='输出验证结果到JSON文件（可选）')
    
    args = parser.parse_args()
    
    print("Loading data...")
    klines = load_kline_data(args.klines)
    nms_results = load_nms_results(args.nms_results)
    
    print(f"Loaded {len(klines)} K-lines")
    print(f"Found {len(nms_results['boxes'])} boxes in NMS results")
    
    # 选择要验证的箱体
    boxes_to_verify = nms_results['boxes']
    if args.box_id is not None:
        boxes_to_verify = [box for box in boxes_to_verify if box['box_id'] == args.box_id]
        if not boxes_to_verify:
            print(f"Error: Box ID {args.box_id} not found")
            return
    
    print(f"\nVerifying {len(boxes_to_verify)} boxes...")
    
    verification_results = []
    issues_found = 0
    
    for box in boxes_to_verify:
        result = verify_box_boundaries(box, klines)
        verification_results.append(result)
        
        if not result['valid']:
            print(f"\n❌ Box {box['box_id']}: {result['error']}")
            issues_found += 1
            continue
        
        # 检查价格差异
        price_comp = result['price_comparison']
        has_issue = (price_comp['min_diff_pct'] > args.tolerance or 
                    price_comp['max_diff_pct'] > args.tolerance)
        
        if has_issue:
            issues_found += 1
            print(f"\n⚠️  Box {result['box_id']} - 价格差异超出容忍度:")
        else:
            print(f"\n✅ Box {result['box_id']} - 验证通过")
        
        # 打印详细信息
        print(f"   时间范围: {result['time_range']['start_time']} ~ {result['time_range']['end_time']}")
        print(f"   索引范围: {result['indices']['left_idx']} ~ {result['indices']['right_idx']} (共{result['indices']['duration']}根K线)")
        print(f"   最低价: 报告={price_comp['reported_min']:.2f}, 实际={price_comp['actual_min']:.2f}, 差异={price_comp['min_diff']:.2f} ({price_comp['min_diff_pct']:.3f}%)")
        print(f"   最高价: 报告={price_comp['reported_max']:.2f}, 实际={price_comp['actual_max']:.2f}, 差异={price_comp['max_diff']:.2f} ({price_comp['max_diff_pct']:.3f}%)")
        
        if has_issue:
            print(f"   极值K线信息:")
            for min_kline in result['klines_sample']['extreme_prices']['min_price_klines']:
                print(f"     最低价 {min_kline['low']} 出现在索引 {min_kline['idx']} ({min_kline['time']})")
            for max_kline in result['klines_sample']['extreme_prices']['max_price_klines']:
                print(f"     最高价 {max_kline['high']} 出现在索引 {max_kline['idx']} ({max_kline['time']})")
    
    # 输出总结
    print(f"\n{'='*60}")
    print(f"验证完成:")
    print(f"  总箱体数: {len(verification_results)}")
    print(f"  发现问题: {issues_found}")
    print(f"  验证通过: {len(verification_results) - issues_found}")
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'total_boxes': len(verification_results),
                    'issues_found': issues_found,
                    'passed': len(verification_results) - issues_found,
                    'tolerance_pct': args.tolerance
                },
                'results': verification_results
            }, f, indent=2, ensure_ascii=False)
        print(f"详细结果已保存到: {args.output}")


if __name__ == "__main__":
    main()
