# TradingView Pine Script 可视化工具使用指南

## 📋 工具概述

`visualize_to_tradingview.py` 是一个命令行工具，用于将NMS箱体检测结果转换为TradingView Pine Script代码，实现箱体的可视化显示。

## 🚀 基本用法

### 1. 使用目标名称（推荐）
```bash
# 使用默认设置
uv run python visualize_to_tradingview.py --target BTCUSDT_4h

# 限制箱体数量
uv run python visualize_to_tradingview.py --target BTCUSDT_4h --max-boxes 50

# 自定义输出目录
uv run python visualize_to_tradingview.py --target BTCUSDT_4h --output-dir results
```

### 2. 指定具体文件路径
```bash
uv run python visualize_to_tradingview.py \
  --input results/BTCUSDT_4h_nms.json \
  --template view.pine \
  --output tmp/my_boxes.pine
```

## 📝 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--target` | string | - | 目标名称，自动推导文件路径 |
| `--input` | string | `results/BTCUSDT_4h_nms.json` | 输入的NMS结果JSON文件 |
| `--template` | string | `view.pine` | Pine Script模板文件 |
| `--output` | string | `tmp/{target}_boxes.pine` | 输出的Pine Script文件 |
| `--max-boxes` | int | 无限制 | 最大箱体数量限制 |
| `--label-format` | string | `Box {id} (Score: {score})` | 标签格式模板 |
| `--output-dir` | string | `tmp` | 输出目录 |

## 🏷️ 标签格式自定义

### 可用变量
- `{id}` - 箱体ID
- `{score}` - 箱体得分
- `{duration}` - 持续时间（K线数量）
- `{price_range}` - 价格范围
- `{min_price}` - 最低价
- `{max_price}` - 最高价

### 示例格式
```bash
# 简洁格式
--label-format "#{id}: {score}"

# 详细格式
--label-format "Box {id} | Score: {score} | Duration: {duration}根K线"

# 中文格式
--label-format "#{id}: {score}分 ({duration}根K线)"

# 价格范围格式
--label-format "#{id}: {min_price}-{max_price} ({score}分)"
```

## 📊 使用示例

### 示例1: 生成前50个最佳箱体
```bash
uv run python visualize_to_tradingview.py \
  --target BTCUSDT_4h \
  --max-boxes 50 \
  --label-format "Top #{id}: {score}分" \
  --output tmp/top50_boxes.pine
```

### 示例2: 生成简洁版本（仅显示得分）
```bash
uv run python visualize_to_tradingview.py \
  --target BTCUSDT_4h \
  --max-boxes 20 \
  --label-format "{score}" \
  --output tmp/simple_boxes.pine
```

### 示例3: 生成详细版本
```bash
uv run python visualize_to_tradingview.py \
  --target BTCUSDT_4h \
  --label-format "Box {id} | {score}分 | {duration}K线 | 范围:{price_range:.0f}" \
  --output tmp/detailed_boxes.pine
```

## 🎯 文件路径规则

### 使用 `--target` 时的自动路径
- **输入文件**: `./results/{target}_nms.json`
- **模板文件**: `./view.pine`
- **输出文件**: `./{output_dir}/{target}_boxes.pine`

### 示例
```bash
# 这个命令
uv run python visualize_to_tradingview.py --target BTCUSDT_4h

# 等价于
uv run python visualize_to_tradingview.py \
  --input ./results/BTCUSDT_4h_nms.json \
  --template ./view.pine \
  --output ./tmp/BTCUSDT_4h_boxes.pine
```

## ⚠️ 注意事项

### 性能考虑
- **推荐箱体数量**: ≤ 100个（最佳性能）
- **最大箱体数量**: ≤ 500个（Pine Script限制）
- **大量箱体**: 可能影响TradingView图表加载速度

### 文件要求
- 输入JSON文件必须是NMS处理后的格式
- 模板文件必须包含正确的PLACEHOLDER标记
- 输出目录会自动创建

### 数据验证
- 工具会自动验证输入文件是否存在
- 检查模板文件格式是否正确
- 确保输出目录可写

## 🔧 高级用法

### 批量处理多个目标
```bash
# 处理多个交易对
for target in BTCUSDT_4h ETHUSDT_4h ADAUSDT_4h; do
  uv run python visualize_to_tradingview.py \
    --target $target \
    --max-boxes 30 \
    --output-dir results/pine_scripts
done
```

### 生成不同版本
```bash
# 生成完整版本
uv run python visualize_to_tradingview.py --target BTCUSDT_4h

# 生成精简版本（前20个）
uv run python visualize_to_tradingview.py \
  --target BTCUSDT_4h \
  --max-boxes 20 \
  --output tmp/BTCUSDT_4h_top20.pine

# 生成超精简版本（前10个，简洁标签）
uv run python visualize_to_tradingview.py \
  --target BTCUSDT_4h \
  --max-boxes 10 \
  --label-format "{score}" \
  --output tmp/BTCUSDT_4h_minimal.pine
```

## 🚀 在TradingView中使用

1. **复制代码**
   ```bash
   cat tmp/BTCUSDT_4h_boxes.pine
   ```

2. **在TradingView中应用**
   - 打开TradingView网站
   - 选择对应的交易对和时间框架
   - 进入Pine Editor
   - 粘贴生成的代码
   - 点击"Add to Chart"

3. **调整图表设置**
   - 确保时间范围包含箱体数据时间段
   - 调整图表缩放以查看所有箱体
   - 根据需要调整标签显示设置

## 📞 故障排除

### 常见错误
1. **文件不存在**: 检查输入文件路径是否正确
2. **权限错误**: 确保输出目录有写入权限
3. **格式错误**: 验证输入JSON文件格式是否正确
4. **内存不足**: 减少箱体数量或使用`--max-boxes`参数

### 调试技巧
```bash
# 查看帮助信息
uv run python visualize_to_tradingview.py --help

# 测试小数据集
uv run python visualize_to_tradingview.py --target BTCUSDT_4h --max-boxes 5

# 检查生成的文件
head -20 tmp/BTCUSDT_4h_boxes.pine
```

---

**更新时间**: 2025-08-02  
**工具版本**: v2.0 (支持argparse)  
**兼容性**: Python 3.7+, TradingView Pine Script v5
