//@version=5
indicator("Multi-Rectangle List with Labels", overlay=true, max_boxes_count=500)

var int[] start_times = array.from(
  timestamp("UTC", 2025, 1, 16, 20, 0), timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 20, 0),
  timestamp("UTC", 2025, 7, 11, 0, 0), timestamp("UTC", 2025, 7, 11, 4, 0), timestamp("UTC", 2025, 7, 12, 4, 0),
  timestamp("UTC", 2025, 1, 17, 0, 0), timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 18, 4, 0),
  timestamp("UTC", 2025, 1, 19, 4, 0), timestamp("UTC", 2025, 2, 4, 8, 0), timestamp("UTC", 2025, 2, 4, 12, 0),
  timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 5, 8, 0),
  timestamp("UTC", 2025, 2, 6, 0, 0), timestamp("UTC", 2025, 7, 10, 20, 0), timestamp("UTC", 2025, 7, 11, 0, 0),
  timestamp("UTC", 2025, 7, 11, 12, 0), timestamp("UTC", 2025, 7, 12, 12, 0), timestamp("UTC", 2025, 7, 12, 16, 0),
  timestamp("UTC", 2025, 7, 13, 8, 0), timestamp("UTC", 2025, 7, 13, 16, 0), timestamp("UTC", 2025, 7, 14, 8, 0),
  timestamp("UTC", 2025, 7, 14, 12, 0), timestamp("UTC", 2025, 7, 14, 16, 0), timestamp("UTC", 2024, 12, 17, 4, 0),
  timestamp("UTC", 2024, 12, 17, 20, 0), timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 20, 0),
  timestamp("UTC", 2024, 12, 19, 0, 0), timestamp("UTC", 2024, 12, 19, 4, 0), timestamp("UTC", 2025, 1, 16, 12, 0),
  timestamp("UTC", 2025, 1, 16, 16, 0), timestamp("UTC", 2025, 1, 17, 0, 0), timestamp("UTC", 2025, 1, 17, 0, 0),
  timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 20, 0),
  timestamp("UTC", 2025, 1, 18, 0, 0), timestamp("UTC", 2025, 1, 18, 12, 0), timestamp("UTC", 2025, 1, 19, 12, 0),
  timestamp("UTC", 2025, 2, 4, 16, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 5, 0, 0),
  timestamp("UTC", 2025, 2, 5, 16, 0), timestamp("UTC", 2025, 2, 5, 20, 0), timestamp("UTC", 2025, 2, 6, 4, 0),
  timestamp("UTC", 2025, 2, 26, 0, 0), timestamp("UTC", 2025, 7, 9, 0, 0), timestamp("UTC", 2025, 7, 9, 16, 0),
  timestamp("UTC", 2025, 7, 9, 20, 0), timestamp("UTC", 2025, 7, 10, 20, 0), timestamp("UTC", 2025, 7, 11, 0, 0),
  timestamp("UTC", 2025, 7, 11, 8, 0), timestamp("UTC", 2025, 7, 11, 16, 0), timestamp("UTC", 2025, 7, 12, 8, 0),
  timestamp("UTC", 2025, 7, 13, 12, 0), timestamp("UTC", 2025, 7, 14, 4, 0), timestamp("UTC", 2025, 7, 14, 8, 0),
  timestamp("UTC", 2025, 7, 14, 12, 0), timestamp("UTC", 2025, 7, 14, 16, 0), timestamp("UTC", 2025, 7, 15, 4, 0),
  timestamp("UTC", 2025, 7, 15, 12, 0), timestamp("UTC", 2025, 7, 15, 12, 0), timestamp("UTC", 2024, 12, 17, 8, 0),
  timestamp("UTC", 2024, 12, 17, 16, 0), timestamp("UTC", 2024, 12, 17, 16, 0), timestamp("UTC", 2024, 12, 17, 20, 0),
  timestamp("UTC", 2024, 12, 18, 0, 0), timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 4, 0),
  timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 16, 0), timestamp("UTC", 2024, 12, 18, 20, 0),
  timestamp("UTC", 2024, 12, 18, 20, 0), timestamp("UTC", 2024, 12, 18, 20, 0), timestamp("UTC", 2024, 12, 19, 8, 0),
  timestamp("UTC", 2024, 12, 19, 16, 0), timestamp("UTC", 2025, 1, 16, 12, 0), timestamp("UTC", 2025, 1, 16, 12, 0),
  timestamp("UTC", 2025, 1, 16, 16, 0), timestamp("UTC", 2025, 1, 16, 16, 0), timestamp("UTC", 2025, 1, 17, 0, 0),
  timestamp("UTC", 2025, 1, 17, 16, 0), timestamp("UTC", 2025, 1, 18, 0, 0), timestamp("UTC", 2025, 1, 18, 4, 0),
  timestamp("UTC", 2025, 1, 18, 4, 0), timestamp("UTC", 2025, 1, 18, 16, 0), timestamp("UTC", 2025, 1, 18, 16, 0),
  timestamp("UTC", 2025, 1, 18, 16, 0), timestamp("UTC", 2025, 1, 19, 4, 0), timestamp("UTC", 2025, 1, 19, 4, 0),
  timestamp("UTC", 2025, 1, 19, 12, 0), timestamp("UTC", 2025, 1, 19, 16, 0), timestamp("UTC", 2025, 1, 20, 0, 0),
  timestamp("UTC", 2025, 1, 20, 4, 0), timestamp("UTC", 2025, 1, 20, 8, 0), timestamp("UTC", 2025, 2, 1, 20, 0),
  timestamp("UTC", 2025, 2, 2, 4, 0), timestamp("UTC", 2025, 2, 2, 12, 0), timestamp("UTC", 2025, 2, 4, 4, 0),
  timestamp("UTC", 2025, 2, 4, 4, 0), timestamp("UTC", 2025, 2, 4, 8, 0), timestamp("UTC", 2025, 2, 4, 8, 0),
  timestamp("UTC", 2025, 2, 4, 16, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 4, 20, 0),
  timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 5, 4, 0),
  timestamp("UTC", 2025, 2, 5, 12, 0), timestamp("UTC", 2025, 2, 5, 12, 0), timestamp("UTC", 2025, 2, 5, 16, 0),
  timestamp("UTC", 2025, 2, 5, 20, 0), timestamp("UTC", 2025, 2, 5, 20, 0), timestamp("UTC", 2025, 2, 5, 20, 0),
  timestamp("UTC", 2025, 2, 6, 4, 0), timestamp("UTC", 2025, 2, 6, 4, 0), timestamp("UTC", 2025, 2, 6, 16, 0),
  timestamp("UTC", 2025, 2, 6, 16, 0), timestamp("UTC", 2025, 2, 6, 20, 0), timestamp("UTC", 2025, 2, 6, 20, 0),
  timestamp("UTC", 2025, 2, 7, 0, 0), timestamp("UTC", 2025, 2, 7, 12, 0), timestamp("UTC", 2025, 2, 7, 12, 0),
  timestamp("UTC", 2025, 2, 7, 12, 0), timestamp("UTC", 2025, 2, 7, 12, 0), timestamp("UTC", 2025, 2, 7, 16, 0),
  timestamp("UTC", 2025, 2, 7, 16, 0), timestamp("UTC", 2025, 2, 7, 16, 0), timestamp("UTC", 2025, 2, 7, 16, 0),
  timestamp("UTC", 2025, 2, 8, 4, 0), timestamp("UTC", 2025, 2, 8, 8, 0), timestamp("UTC", 2025, 2, 8, 12, 0),
  timestamp("UTC", 2025, 2, 8, 12, 0), timestamp("UTC", 2025, 2, 8, 12, 0), timestamp("UTC", 2025, 2, 9, 0, 0),
  timestamp("UTC", 2025, 2, 9, 8, 0), timestamp("UTC", 2025, 2, 9, 12, 0), timestamp("UTC", 2025, 2, 9, 16, 0),
  timestamp("UTC", 2025, 2, 9, 20, 0), timestamp("UTC", 2025, 2, 10, 4, 0), timestamp("UTC", 2025, 2, 10, 8, 0),
  timestamp("UTC", 2025, 2, 10, 12, 0), timestamp("UTC", 2025, 2, 11, 0, 0), timestamp("UTC", 2025, 2, 11, 4, 0),
  timestamp("UTC", 2025, 2, 11, 4, 0), timestamp("UTC", 2025, 2, 11, 8, 0), timestamp("UTC", 2025, 5, 9, 4, 0),
  timestamp("UTC", 2025, 5, 9, 4, 0), timestamp("UTC", 2025, 5, 13, 0, 0), timestamp("UTC", 2025, 7, 9, 4, 0),
  timestamp("UTC", 2025, 7, 9, 16, 0), timestamp("UTC", 2025, 7, 9, 20, 0), timestamp("UTC", 2025, 7, 10, 12, 0),
  timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 20, 0),
  timestamp("UTC", 2025, 7, 11, 0, 0), timestamp("UTC", 2025, 7, 11, 4, 0), timestamp("UTC", 2025, 7, 11, 8, 0),
  timestamp("UTC", 2025, 7, 12, 0, 0), timestamp("UTC", 2025, 7, 12, 4, 0), timestamp("UTC", 2025, 7, 12, 16, 0),
  timestamp("UTC", 2025, 7, 12, 20, 0), timestamp("UTC", 2025, 7, 13, 0, 0), timestamp("UTC", 2025, 7, 13, 16, 0),
  timestamp("UTC", 2025, 7, 13, 20, 0), timestamp("UTC", 2025, 7, 14, 8, 0), timestamp("UTC", 2025, 7, 14, 8, 0),
  timestamp("UTC", 2025, 7, 14, 12, 0), timestamp("UTC", 2025, 7, 14, 16, 0), timestamp("UTC", 2025, 7, 14, 16, 0),
  timestamp("UTC", 2025, 7, 16, 4, 0), timestamp("UTC", 2025, 7, 16, 20, 0), timestamp("UTC", 2025, 7, 17, 0, 0),
  timestamp("UTC", 2025, 7, 17, 0, 0), timestamp("UTC", 2025, 7, 17, 12, 0), timestamp("UTC", 2025, 7, 18, 0, 0),
  timestamp("UTC", 2025, 7, 18, 0, 0), timestamp("UTC", 2025, 7, 18, 8, 0), timestamp("UTC", 2025, 7, 18, 8, 0),
  timestamp("UTC", 2025, 7, 18, 8, 0), timestamp("UTC", 2024, 11, 3, 4, 0), timestamp("UTC", 2024, 11, 6, 0, 0),
  timestamp("UTC", 2024, 11, 6, 12, 0), timestamp("UTC", 2024, 11, 8, 12, 0), timestamp("UTC", 2024, 11, 10, 8, 0),
  timestamp("UTC", 2024, 11, 15, 20, 0), timestamp("UTC", 2024, 11, 19, 4, 0), timestamp("UTC", 2024, 12, 17, 4, 0),
  timestamp("UTC", 2024, 12, 17, 16, 0), timestamp("UTC", 2024, 12, 17, 20, 0), timestamp("UTC", 2024, 12, 17, 20, 0),
  timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 4, 0), timestamp("UTC", 2024, 12, 18, 16, 0),
  timestamp("UTC", 2024, 12, 18, 16, 0), timestamp("UTC", 2024, 12, 18, 20, 0), timestamp("UTC", 2024, 12, 18, 20, 0),
  timestamp("UTC", 2024, 12, 19, 0, 0), timestamp("UTC", 2024, 12, 19, 12, 0), timestamp("UTC", 2024, 12, 19, 16, 0),
  timestamp("UTC", 2024, 12, 19, 20, 0), timestamp("UTC", 2024, 12, 19, 20, 0), timestamp("UTC", 2024, 12, 20, 4, 0),
  timestamp("UTC", 2024, 12, 21, 4, 0), timestamp("UTC", 2025, 1, 15, 16, 0), timestamp("UTC", 2025, 1, 16, 12, 0),
  timestamp("UTC", 2025, 1, 16, 20, 0), timestamp("UTC", 2025, 1, 16, 20, 0), timestamp("UTC", 2025, 1, 17, 4, 0),
  timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 17, 16, 0), timestamp("UTC", 2025, 1, 18, 0, 0),
  timestamp("UTC", 2025, 1, 18, 12, 0), timestamp("UTC", 2025, 1, 18, 12, 0), timestamp("UTC", 2025, 1, 18, 12, 0),
  timestamp("UTC", 2025, 1, 18, 20, 0), timestamp("UTC", 2025, 1, 19, 0, 0), timestamp("UTC", 2025, 1, 19, 4, 0),
  timestamp("UTC", 2025, 1, 19, 8, 0), timestamp("UTC", 2025, 1, 19, 16, 0), timestamp("UTC", 2025, 1, 19, 16, 0),
  timestamp("UTC", 2025, 1, 19, 20, 0), timestamp("UTC", 2025, 1, 20, 0, 0), timestamp("UTC", 2025, 1, 20, 8, 0),
  timestamp("UTC", 2025, 1, 20, 16, 0), timestamp("UTC", 2025, 2, 2, 20, 0), timestamp("UTC", 2025, 2, 3, 0, 0),
  timestamp("UTC", 2025, 2, 3, 4, 0), timestamp("UTC", 2025, 2, 3, 4, 0), timestamp("UTC", 2025, 2, 3, 4, 0),
  timestamp("UTC", 2025, 2, 3, 4, 0), timestamp("UTC", 2025, 2, 3, 8, 0), timestamp("UTC", 2025, 2, 3, 8, 0),
  timestamp("UTC", 2025, 2, 3, 8, 0), timestamp("UTC", 2025, 2, 3, 8, 0), timestamp("UTC", 2025, 2, 3, 20, 0),
  timestamp("UTC", 2025, 2, 4, 4, 0), timestamp("UTC", 2025, 2, 4, 20, 0), timestamp("UTC", 2025, 2, 6, 4, 0),
  timestamp("UTC", 2025, 2, 6, 8, 0), timestamp("UTC", 2025, 2, 6, 12, 0), timestamp("UTC", 2025, 2, 7, 0, 0),
  timestamp("UTC", 2025, 2, 7, 0, 0), timestamp("UTC", 2025, 2, 7, 8, 0), timestamp("UTC", 2025, 2, 7, 12, 0),
  timestamp("UTC", 2025, 2, 7, 12, 0), timestamp("UTC", 2025, 2, 7, 16, 0), timestamp("UTC", 2025, 2, 7, 16, 0),
  timestamp("UTC", 2025, 2, 8, 8, 0), timestamp("UTC", 2025, 2, 8, 8, 0), timestamp("UTC", 2025, 2, 9, 0, 0),
  timestamp("UTC", 2025, 2, 9, 16, 0), timestamp("UTC", 2025, 2, 10, 4, 0), timestamp("UTC", 2025, 2, 11, 0, 0),
  timestamp("UTC", 2025, 2, 11, 16, 0), timestamp("UTC", 2025, 2, 12, 0, 0), timestamp("UTC", 2025, 2, 20, 12, 0),
  timestamp("UTC", 2025, 2, 21, 20, 0), timestamp("UTC", 2025, 2, 23, 8, 0), timestamp("UTC", 2025, 2, 25, 20, 0),
  timestamp("UTC", 2025, 2, 26, 16, 0), timestamp("UTC", 2025, 3, 2, 20, 0), timestamp("UTC", 2025, 3, 3, 16, 0),
  timestamp("UTC", 2025, 3, 5, 16, 0), timestamp("UTC", 2025, 3, 6, 8, 0), timestamp("UTC", 2025, 3, 7, 8, 0),
  timestamp("UTC", 2025, 3, 7, 16, 0), timestamp("UTC", 2025, 3, 8, 12, 0), timestamp("UTC", 2025, 3, 9, 0, 0),
  timestamp("UTC", 2025, 4, 25, 8, 0), timestamp("UTC", 2025, 4, 25, 8, 0), timestamp("UTC", 2025, 5, 9, 16, 0),
  timestamp("UTC", 2025, 5, 9, 16, 0), timestamp("UTC", 2025, 5, 9, 20, 0), timestamp("UTC", 2025, 5, 10, 0, 0),
  timestamp("UTC", 2025, 5, 10, 12, 0), timestamp("UTC", 2025, 5, 13, 12, 0), timestamp("UTC", 2025, 5, 27, 12, 0),
  timestamp("UTC", 2025, 7, 9, 4, 0), timestamp("UTC", 2025, 7, 9, 16, 0), timestamp("UTC", 2025, 7, 9, 20, 0),
  timestamp("UTC", 2025, 7, 10, 4, 0), timestamp("UTC", 2025, 7, 10, 16, 0), timestamp("UTC", 2025, 7, 10, 20, 0),
  timestamp("UTC", 2025, 7, 11, 4, 0), timestamp("UTC", 2025, 7, 12, 8, 0), timestamp("UTC", 2025, 7, 12, 20, 0),
  timestamp("UTC", 2025, 7, 13, 8, 0), timestamp("UTC", 2025, 7, 14, 0, 0), timestamp("UTC", 2025, 7, 14, 4, 0),
  timestamp("UTC", 2025, 7, 14, 8, 0), timestamp("UTC", 2025, 7, 14, 8, 0), timestamp("UTC", 2025, 7, 14, 12, 0),
  timestamp("UTC", 2025, 7, 14, 12, 0), timestamp("UTC", 2025, 7, 14, 16, 0), timestamp("UTC", 2025, 7, 14, 16, 0),
  timestamp("UTC", 2025, 7, 14, 20, 0), timestamp("UTC", 2025, 7, 15, 8, 0), timestamp("UTC", 2025, 7, 15, 12, 0),
  timestamp("UTC", 2025, 7, 15, 16, 0), timestamp("UTC", 2025, 7, 15, 20, 0), timestamp("UTC", 2025, 7, 16, 0, 0),
  timestamp("UTC", 2025, 7, 16, 16, 0), timestamp("UTC", 2025, 7, 17, 8, 0), timestamp("UTC", 2025, 7, 17, 20, 0),
  timestamp("UTC", 2025, 7, 18, 8, 0), timestamp("UTC", 2025, 7, 18, 20, 0), timestamp("UTC", 2025, 7, 19, 0, 0),
  timestamp("UTC", 2025, 7, 19, 12, 0), timestamp("UTC", 2025, 7, 20, 4, 0), timestamp("UTC", 2025, 7, 20, 8, 0)
  )
var int[] end_times = array.from(
  timestamp("UTC", 2025, 2, 1, 12, 0), timestamp("UTC", 2025, 7, 30, 20, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0),
  timestamp("UTC", 2025, 1, 30, 12, 0), timestamp("UTC", 2025, 1, 26, 8, 0), timestamp("UTC", 2025, 2, 1, 20, 0),
  timestamp("UTC", 2025, 2, 1, 8, 0), timestamp("UTC", 2025, 2, 17, 20, 0), timestamp("UTC", 2025, 2, 23, 12, 0),
  timestamp("UTC", 2025, 2, 17, 4, 0), timestamp("UTC", 2025, 2, 22, 16, 0), timestamp("UTC", 2025, 2, 18, 0, 0),
  timestamp("UTC", 2025, 2, 17, 8, 0), timestamp("UTC", 2025, 7, 28, 16, 0), timestamp("UTC", 2025, 7, 27, 20, 0),
  timestamp("UTC", 2025, 7, 29, 4, 0), timestamp("UTC", 2025, 7, 28, 4, 0), timestamp("UTC", 2025, 7, 30, 0, 0),
  timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 30, 20, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 31, 0, 0), timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 1, 15, 16, 0),
  timestamp("UTC", 2025, 1, 15, 12, 0), timestamp("UTC", 2025, 1, 15, 16, 0), timestamp("UTC", 2025, 1, 15, 12, 0),
  timestamp("UTC", 2025, 1, 17, 12, 0), timestamp("UTC", 2025, 1, 17, 16, 0), timestamp("UTC", 2025, 1, 27, 0, 0),
  timestamp("UTC", 2025, 1, 27, 0, 0), timestamp("UTC", 2025, 1, 27, 8, 0), timestamp("UTC", 2025, 1, 28, 12, 0),
  timestamp("UTC", 2025, 1, 25, 8, 0), timestamp("UTC", 2025, 1, 31, 12, 0), timestamp("UTC", 2025, 1, 26, 20, 0),
  timestamp("UTC", 2025, 1, 30, 0, 0), timestamp("UTC", 2025, 1, 30, 20, 0), timestamp("UTC", 2025, 1, 31, 4, 0),
  timestamp("UTC", 2025, 2, 15, 20, 0), timestamp("UTC", 2025, 2, 15, 0, 0), timestamp("UTC", 2025, 2, 16, 0, 0),
  timestamp("UTC", 2025, 2, 14, 16, 0), timestamp("UTC", 2025, 2, 15, 12, 0), timestamp("UTC", 2025, 2, 16, 8, 0),
  timestamp("UTC", 2025, 4, 21, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0), timestamp("UTC", 2025, 7, 30, 16, 0),
  timestamp("UTC", 2025, 7, 30, 16, 0), timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 7, 25, 0, 0),
  timestamp("UTC", 2025, 7, 25, 8, 0), timestamp("UTC", 2025, 7, 26, 16, 0), timestamp("UTC", 2025, 7, 25, 0, 0),
  timestamp("UTC", 2025, 7, 28, 20, 0), timestamp("UTC", 2025, 7, 27, 20, 0), timestamp("UTC", 2025, 7, 28, 8, 0),
  timestamp("UTC", 2025, 7, 28, 0, 0), timestamp("UTC", 2025, 7, 28, 4, 0), timestamp("UTC", 2025, 8, 1, 0, 0),
  timestamp("UTC", 2025, 7, 29, 12, 0), timestamp("UTC", 2025, 7, 31, 16, 0), timestamp("UTC", 2025, 1, 13, 4, 0),
  timestamp("UTC", 2025, 1, 11, 4, 0), timestamp("UTC", 2025, 1, 13, 12, 0), timestamp("UTC", 2025, 1, 12, 16, 0),
  timestamp("UTC", 2025, 1, 9, 8, 0), timestamp("UTC", 2025, 1, 8, 16, 0), timestamp("UTC", 2025, 1, 10, 8, 0),
  timestamp("UTC", 2025, 1, 13, 0, 0), timestamp("UTC", 2025, 1, 13, 12, 0), timestamp("UTC", 2025, 1, 9, 0, 0),
  timestamp("UTC", 2025, 1, 10, 12, 0), timestamp("UTC", 2025, 1, 13, 4, 0), timestamp("UTC", 2025, 1, 13, 12, 0),
  timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 25, 12, 0), timestamp("UTC", 2025, 1, 31, 4, 0),
  timestamp("UTC", 2025, 1, 24, 12, 0), timestamp("UTC", 2025, 1, 25, 12, 0), timestamp("UTC", 2025, 1, 24, 16, 0),
  timestamp("UTC", 2025, 1, 27, 20, 0), timestamp("UTC", 2025, 1, 25, 20, 0), timestamp("UTC", 2025, 1, 27, 4, 0),
  timestamp("UTC", 2025, 1, 28, 12, 0), timestamp("UTC", 2025, 1, 27, 0, 0), timestamp("UTC", 2025, 1, 27, 16, 0),
  timestamp("UTC", 2025, 1, 29, 4, 0), timestamp("UTC", 2025, 1, 27, 4, 0), timestamp("UTC", 2025, 1, 28, 4, 0),
  timestamp("UTC", 2025, 1, 26, 20, 0), timestamp("UTC", 2025, 1, 28, 16, 0), timestamp("UTC", 2025, 1, 30, 4, 0),
  timestamp("UTC", 2025, 2, 1, 20, 0), timestamp("UTC", 2025, 2, 1, 8, 0), timestamp("UTC", 2025, 2, 23, 0, 0),
  timestamp("UTC", 2025, 2, 17, 20, 0), timestamp("UTC", 2025, 2, 16, 0, 0), timestamp("UTC", 2025, 2, 18, 16, 0),
  timestamp("UTC", 2025, 2, 25, 8, 0), timestamp("UTC", 2025, 2, 24, 20, 0), timestamp("UTC", 2025, 2, 25, 4, 0),
  timestamp("UTC", 2025, 2, 26, 0, 0), timestamp("UTC", 2025, 2, 13, 20, 0), timestamp("UTC", 2025, 2, 18, 16, 0),
  timestamp("UTC", 2025, 2, 24, 16, 0), timestamp("UTC", 2025, 2, 25, 0, 0), timestamp("UTC", 2025, 2, 13, 4, 0),
  timestamp("UTC", 2025, 2, 25, 4, 0), timestamp("UTC", 2025, 2, 25, 8, 0), timestamp("UTC", 2025, 2, 21, 16, 0),
  timestamp("UTC", 2025, 2, 13, 8, 0), timestamp("UTC", 2025, 2, 23, 16, 0), timestamp("UTC", 2025, 2, 24, 20, 0),
  timestamp("UTC", 2025, 2, 18, 12, 0), timestamp("UTC", 2025, 2, 25, 16, 0), timestamp("UTC", 2025, 2, 15, 16, 0),
  timestamp("UTC", 2025, 2, 22, 12, 0), timestamp("UTC", 2025, 2, 17, 20, 0), timestamp("UTC", 2025, 2, 24, 16, 0),
  timestamp("UTC", 2025, 2, 25, 0, 0), timestamp("UTC", 2025, 2, 21, 16, 0), timestamp("UTC", 2025, 2, 23, 12, 0),
  timestamp("UTC", 2025, 2, 25, 4, 0), timestamp("UTC", 2025, 2, 25, 8, 0), timestamp("UTC", 2025, 2, 21, 16, 0),
  timestamp("UTC", 2025, 2, 23, 8, 0), timestamp("UTC", 2025, 2, 24, 20, 0), timestamp("UTC", 2025, 2, 25, 12, 0),
  timestamp("UTC", 2025, 2, 24, 16, 0), timestamp("UTC", 2025, 2, 25, 4, 0), timestamp("UTC", 2025, 2, 22, 8, 0),
  timestamp("UTC", 2025, 2, 25, 0, 0), timestamp("UTC", 2025, 2, 25, 8, 0), timestamp("UTC", 2025, 2, 23, 16, 0),
  timestamp("UTC", 2025, 2, 21, 16, 0), timestamp("UTC", 2025, 2, 24, 20, 0), timestamp("UTC", 2025, 2, 22, 20, 0),
  timestamp("UTC", 2025, 2, 24, 12, 0), timestamp("UTC", 2025, 2, 25, 0, 0), timestamp("UTC", 2025, 2, 22, 0, 0),
  timestamp("UTC", 2025, 2, 23, 12, 0), timestamp("UTC", 2025, 2, 22, 16, 0), timestamp("UTC", 2025, 2, 24, 8, 0),
  timestamp("UTC", 2025, 2, 24, 20, 0), timestamp("UTC", 2025, 2, 21, 16, 0), timestamp("UTC", 2025, 5, 19, 8, 0),
  timestamp("UTC", 2025, 5, 20, 12, 0), timestamp("UTC", 2025, 5, 19, 20, 0), timestamp("UTC", 2025, 7, 29, 8, 0),
  timestamp("UTC", 2025, 7, 27, 20, 0), timestamp("UTC", 2025, 7, 27, 20, 0), timestamp("UTC", 2025, 7, 29, 4, 0),
  timestamp("UTC", 2025, 7, 24, 4, 0), timestamp("UTC", 2025, 7, 27, 20, 0), timestamp("UTC", 2025, 7, 23, 4, 0),
  timestamp("UTC", 2025, 7, 22, 16, 0), timestamp("UTC", 2025, 7, 23, 4, 0), timestamp("UTC", 2025, 7, 24, 8, 0),
  timestamp("UTC", 2025, 7, 22, 16, 0), timestamp("UTC", 2025, 7, 23, 20, 0), timestamp("UTC", 2025, 7, 25, 4, 0),
  timestamp("UTC", 2025, 7, 24, 12, 0), timestamp("UTC", 2025, 7, 27, 0, 0), timestamp("UTC", 2025, 7, 25, 0, 0),
  timestamp("UTC", 2025, 7, 25, 8, 0), timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 8, 1, 0, 0),
  timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 7, 25, 4, 0),
  timestamp("UTC", 2025, 7, 30, 8, 0), timestamp("UTC", 2025, 7, 31, 8, 0), timestamp("UTC", 2025, 7, 29, 12, 0),
  timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 7, 30, 12, 0), timestamp("UTC", 2025, 7, 29, 16, 0),
  timestamp("UTC", 2025, 7, 31, 12, 0), timestamp("UTC", 2025, 7, 29, 12, 0), timestamp("UTC", 2025, 7, 30, 20, 0),
  timestamp("UTC", 2025, 8, 1, 0, 0), timestamp("UTC", 2025, 2, 24, 20, 0), timestamp("UTC", 2025, 2, 27, 12, 0),
  timestamp("UTC", 2025, 2, 26, 20, 0), timestamp("UTC", 2025, 3, 2, 8, 0), timestamp("UTC", 2025, 3, 3, 20, 0),
  timestamp("UTC", 2025, 3, 10, 12, 0), timestamp("UTC", 2025, 3, 13, 16, 0), timestamp("UTC", 2025, 1, 6, 0, 0),
  timestamp("UTC", 2025, 1, 17, 8, 0), timestamp("UTC", 2025, 1, 4, 16, 0), timestamp("UTC", 2025, 1, 6, 16, 0),
  timestamp("UTC", 2025, 1, 3, 16, 0), timestamp("UTC", 2025, 1, 5, 16, 0), timestamp("UTC", 2025, 1, 4, 12, 0),
  timestamp("UTC", 2025, 1, 6, 16, 0), timestamp("UTC", 2025, 1, 3, 16, 0), timestamp("UTC", 2025, 1, 5, 12, 0),
  timestamp("UTC", 2025, 1, 7, 0, 0), timestamp("UTC", 2025, 1, 4, 12, 0), timestamp("UTC", 2025, 1, 3, 16, 0),
  timestamp("UTC", 2025, 1, 3, 16, 0), timestamp("UTC", 2025, 1, 11, 16, 0), timestamp("UTC", 2025, 1, 9, 16, 0),
  timestamp("UTC", 2025, 1, 15, 16, 0), timestamp("UTC", 2025, 2, 1, 20, 0), timestamp("UTC", 2025, 2, 2, 16, 0),
  timestamp("UTC", 2025, 1, 23, 16, 0), timestamp("UTC", 2025, 2, 3, 0, 0), timestamp("UTC", 2025, 1, 23, 8, 0),
  timestamp("UTC", 2025, 1, 24, 0, 0), timestamp("UTC", 2025, 1, 24, 12, 0), timestamp("UTC", 2025, 1, 25, 0, 0),
  timestamp("UTC", 2025, 1, 24, 12, 0), timestamp("UTC", 2025, 1, 25, 8, 0), timestamp("UTC", 2025, 1, 26, 4, 0),
  timestamp("UTC", 2025, 1, 24, 20, 0), timestamp("UTC", 2025, 1, 25, 16, 0), timestamp("UTC", 2025, 1, 26, 8, 0),
  timestamp("UTC", 2025, 1, 25, 0, 0), timestamp("UTC", 2025, 1, 25, 8, 0), timestamp("UTC", 2025, 1, 26, 0, 0),
  timestamp("UTC", 2025, 1, 27, 8, 0), timestamp("UTC", 2025, 1, 28, 0, 0), timestamp("UTC", 2025, 1, 30, 0, 0),
  timestamp("UTC", 2025, 2, 1, 8, 0), timestamp("UTC", 2025, 2, 27, 8, 0), timestamp("UTC", 2025, 2, 17, 0, 0),
  timestamp("UTC", 2025, 2, 14, 8, 0), timestamp("UTC", 2025, 2, 15, 16, 0), timestamp("UTC", 2025, 2, 17, 4, 0),
  timestamp("UTC", 2025, 2, 23, 8, 0), timestamp("UTC", 2025, 2, 14, 12, 0), timestamp("UTC", 2025, 2, 15, 20, 0),
  timestamp("UTC", 2025, 2, 17, 8, 0), timestamp("UTC", 2025, 2, 22, 8, 0), timestamp("UTC", 2025, 2, 20, 16, 0),
  timestamp("UTC", 2025, 2, 20, 16, 0), timestamp("UTC", 2025, 2, 20, 16, 0), timestamp("UTC", 2025, 2, 12, 20, 0),
  timestamp("UTC", 2025, 2, 15, 0, 0), timestamp("UTC", 2025, 2, 20, 16, 0), timestamp("UTC", 2025, 2, 14, 16, 0),
  timestamp("UTC", 2025, 2, 16, 16, 0), timestamp("UTC", 2025, 2, 15, 4, 0), timestamp("UTC", 2025, 2, 16, 0, 0),
  timestamp("UTC", 2025, 2, 17, 8, 0), timestamp("UTC", 2025, 2, 17, 12, 0), timestamp("UTC", 2025, 2, 20, 16, 0),
  timestamp("UTC", 2025, 2, 18, 0, 0), timestamp("UTC", 2025, 2, 21, 8, 0), timestamp("UTC", 2025, 2, 20, 16, 0),
  timestamp("UTC", 2025, 2, 21, 8, 0), timestamp("UTC", 2025, 2, 20, 20, 0), timestamp("UTC", 2025, 2, 21, 4, 0),
  timestamp("UTC", 2025, 2, 23, 8, 0), timestamp("UTC", 2025, 2, 22, 4, 0), timestamp("UTC", 2025, 4, 22, 4, 0),
  timestamp("UTC", 2025, 4, 21, 12, 0), timestamp("UTC", 2025, 4, 25, 0, 0), timestamp("UTC", 2025, 4, 27, 0, 0),
  timestamp("UTC", 2025, 4, 15, 12, 0), timestamp("UTC", 2025, 4, 21, 8, 0), timestamp("UTC", 2025, 4, 21, 0, 0),
  timestamp("UTC", 2025, 4, 22, 20, 0), timestamp("UTC", 2025, 4, 21, 0, 0), timestamp("UTC", 2025, 4, 24, 12, 0),
  timestamp("UTC", 2025, 4, 21, 0, 0), timestamp("UTC", 2025, 4, 17, 16, 0), timestamp("UTC", 2025, 4, 22, 12, 0),
  timestamp("UTC", 2025, 5, 1, 8, 0), timestamp("UTC", 2025, 5, 1, 12, 0), timestamp("UTC", 2025, 5, 18, 16, 0),
  timestamp("UTC", 2025, 5, 18, 20, 0), timestamp("UTC", 2025, 5, 20, 0, 0), timestamp("UTC", 2025, 5, 19, 0, 0),
  timestamp("UTC", 2025, 5, 19, 12, 0), timestamp("UTC", 2025, 5, 19, 12, 0), timestamp("UTC", 2025, 7, 7, 16, 0),
  timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 7, 25, 12, 0), timestamp("UTC", 2025, 7, 25, 0, 0),
  timestamp("UTC", 2025, 7, 26, 8, 0), timestamp("UTC", 2025, 7, 25, 20, 0), timestamp("UTC", 2025, 7, 26, 16, 0),
  timestamp("UTC", 2025, 7, 21, 16, 0), timestamp("UTC", 2025, 7, 21, 16, 0), timestamp("UTC", 2025, 7, 23, 0, 0),
  timestamp("UTC", 2025, 7, 23, 16, 0), timestamp("UTC", 2025, 7, 24, 4, 0), timestamp("UTC", 2025, 7, 23, 4, 0),
  timestamp("UTC", 2025, 7, 22, 16, 0), timestamp("UTC", 2025, 7, 23, 16, 0), timestamp("UTC", 2025, 7, 22, 16, 0),
  timestamp("UTC", 2025, 7, 23, 16, 0), timestamp("UTC", 2025, 7, 22, 16, 0), timestamp("UTC", 2025, 7, 23, 16, 0),
  timestamp("UTC", 2025, 7, 24, 12, 0), timestamp("UTC", 2025, 7, 26, 12, 0), timestamp("UTC", 2025, 7, 25, 12, 0),
  timestamp("UTC", 2025, 7, 27, 12, 0), timestamp("UTC", 2025, 7, 25, 0, 0), timestamp("UTC", 2025, 7, 28, 12, 0),
  timestamp("UTC", 2025, 7, 27, 20, 0), timestamp("UTC", 2025, 7, 28, 12, 0), timestamp("UTC", 2025, 7, 27, 20, 0),
  timestamp("UTC", 2025, 7, 27, 16, 0), timestamp("UTC", 2025, 7, 31, 20, 0), timestamp("UTC", 2025, 7, 30, 4, 0),
  timestamp("UTC", 2025, 7, 31, 0, 0), timestamp("UTC", 2025, 7, 31, 16, 0), timestamp("UTC", 2025, 7, 30, 16, 0)
  )
var float[] min_prices = array.from(
  97680.0, 111267.7, 113208.4,
  114667.3, 114239.0, 114667.3,
  97680.0, 99501.0, 97680.0,
  97680.0, 94003.9, 93321.6,
  94003.9, 93321.6, 94003.9,
  94003.9, 113208.4, 114667.3,
  114667.3, 114667.3, 114667.3,
  114239.0, 114667.3, 114667.3,
  114667.3, 114667.3, 88909.0,
  88909.0, 88909.0, 88909.0,
  88909.0, 88909.0, 97293.6,
  99023.9, 97680.0, 97680.0,
  99501.0, 97680.0, 99501.0,
  97680.0, 97680.0, 97680.0,
  94003.9, 94003.9, 94003.9,
  94003.9, 94003.9, 94003.9,
  74457.0, 108273.5, 108975.9,
  110432.2, 113208.4, 115186.7,
  114667.3, 114667.3, 115300.0,
  114667.3, 114667.3, 114667.3,
  114667.3, 114667.3, 114239.0,
  114667.3, 114667.3, 91055.8,
  91055.8, 88909.0, 91055.8,
  91510.0, 91510.0, 91055.8,
  91055.8, 88909.0, 91510.0,
  91055.8, 91055.8, 88909.0,
  88909.0, 97293.6, 97293.6,
  99023.9, 99023.9, 99501.0,
  97680.0, 99501.0, 97680.0,
  97680.0, 99501.0, 97680.0,
  97680.0, 97680.0, 97680.0,
  99501.0, 97680.0, 97680.0,
  97680.0, 97680.0, 91130.3,
  91130.3, 91130.3, 93321.6,
  86800.0, 91300.0, 88131.6,
  86020.4, 94003.9, 93321.6,
  93321.6, 90850.0, 94003.9,
  88131.6, 86800.0, 93321.6,
  94003.9, 93321.6, 91300.0,
  94003.9, 86020.4, 94003.9,
  93321.6, 94003.9, 93321.6,
  90850.0, 93321.6, 93321.6,
  88131.6, 86800.0, 93321.6,
  93321.6, 91300.0, 86020.4,
  93321.6, 88131.6, 93321.6,
  90850.0, 86800.0, 93321.6,
  93321.6, 91300.0, 93321.6,
  93321.6, 90850.0, 93321.6,
  93321.6, 93321.6, 93321.6,
  91300.0, 93321.6, 100678.0,
  100678.0, 101270.8, 108273.5,
  108975.9, 110432.2, 110432.2,
  111267.7, 111267.7, 113208.4,
  115186.7, 115678.1, 115678.1,
  115678.1, 115678.1, 114667.3,
  115678.1, 114667.3, 115300.0,
  114667.3, 115300.0, 114239.0,
  115300.0, 115300.0, 114667.3,
  114667.3, 114667.3, 114667.3,
  114239.0, 114667.3, 114667.3,
  114667.3, 114667.3, 114667.3,
  114239.0, 66810.0, 69272.7,
  73500.0, 75620.1, 78200.2,
  78200.2, 76560.0, 91510.0,
  88909.0, 91510.0, 91510.0,
  91510.0, 91510.0, 91510.0,
  91510.0, 91510.0, 91510.0,
  91510.0, 91510.0, 91510.0,
  91510.0, 91055.8, 91510.0,
  88909.0, 97293.6, 96750.1,
  99326.3, 91130.3, 99501.0,
  99501.0, 99501.0, 99501.0,
  99501.0, 99501.0, 99501.0,
  99501.0, 99501.0, 99501.0,
  99501.0, 99501.0, 99501.0,
  97680.0, 97680.0, 97680.0,
  97680.0, 82222.0, 91130.3,
  92595.7, 92595.7, 92595.7,
  92595.7, 94003.9, 94003.9,
  94003.9, 93321.6, 93321.6,
  93321.6, 93321.6, 94003.9,
  94003.9, 93321.6, 94003.9,
  94003.9, 94003.9, 94003.9,
  94003.9, 94003.9, 93321.6,
  94003.9, 93321.6, 93321.6,
  93321.6, 93321.6, 93321.6,
  93321.6, 93321.6, 74457.0,
  74457.0, 74457.0, 74457.0,
  74457.0, 74457.0, 74457.0,
  74457.0, 74457.0, 74457.0,
  74457.0, 74457.0, 74457.0,
  92700.0, 92700.0, 100678.0,
  100678.0, 100678.0, 100678.0,
  100678.0, 101270.8, 98115.4,
  108273.5, 108975.9, 110432.2,
  110432.2, 111267.7, 113208.4,
  115678.1, 115678.1, 115678.1,
  115678.1, 115678.1, 115678.1,
  115678.1, 115678.1, 115678.1,
  115678.1, 115678.1, 115678.1,
  115678.1, 114667.3, 114667.3,
  114667.3, 115300.0, 114667.3,
  114667.3, 114667.3, 114667.3,
  114667.3, 114667.3, 114667.3,
  114667.3, 114667.3, 114667.3
  )
var float[] max_prices = array.from(
  110000.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 100743.6, 100743.6,
  100112.4, 100112.4, 100112.4,
  100112.4, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 122772.7,
  122113.9, 120951.5, 108366.8,
  106877.3, 105343.8, 102954.8,
  104198.9, 105784.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  100743.6, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  94971.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 122772.7,
  122113.9, 120951.5, 120951.5,
  120951.5, 120951.5, 108366.8,
  107596.0, 107596.0, 106877.3,
  106484.6, 105343.8, 105343.8,
  105343.8, 104959.4, 102954.8,
  102954.8, 102954.8, 102762.1,
  102762.1, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 109300.8, 102456.0,
  102456.0, 102456.0, 100743.6,
  100743.6, 100743.6, 100743.6,
  100743.6, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 99431.5,
  99431.5, 99431.5, 107114.3,
  107114.3, 107114.3, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 122772.7, 122772.7,
  122113.9, 120951.5, 120951.5,
  120951.5, 120951.5, 120951.5,
  120951.5, 120951.5, 120846.1,
  120846.1, 120300.1, 120300.1,
  120300.1, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 108366.8,
  107596.0, 106877.3, 106877.3,
  105343.8, 105343.8, 104959.4,
  104959.4, 102954.8, 102954.8,
  102762.1, 102756.7, 101162.5,
  99950.0, 102762.1, 102762.1,
  102762.1, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 110000.0,
  110000.0, 110000.0, 109300.8,
  107241.6, 102456.0, 102456.0,
  102456.0, 102456.0, 102456.0,
  102456.0, 102456.0, 102456.0,
  102456.0, 102456.0, 102456.0,
  100743.6, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 100112.4, 100112.4,
  100112.4, 98792.8, 98792.8,
  98792.8, 98888.0, 98792.8,
  98888.0, 98792.8, 98792.8,
  99431.5, 99431.5, 99431.5,
  96988.0, 96470.0, 95769.6,
  94971.0, 94800.0, 92780.3,
  94142.5, 91642.3, 94904.3,
  88990.0, 88752.0, 91373.0,
  96351.8, 97388.0, 105999.7,
  106629.8, 107114.3, 107114.3,
  107114.3, 107114.3, 110700.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  123300.0, 123300.0, 123300.0,
  122772.7, 122772.7, 122113.9,
  122113.9, 120951.5, 120951.5,
  120951.5, 120951.5, 120951.5,
  120951.5, 120951.5, 120951.5,
  120951.5, 120951.5, 120951.5,
  120300.1, 120300.1, 120300.1,
  120300.1, 120300.1, 120300.1
  )

// 硬编码标签文本
var string[] label_texts = array.from(
  "Box 0 (Score: 10.0)", "Box 1 (Score: 10.0)", "Box 2 (Score: 10.0)",
  "Box 3 (Score: 10.0)", "Box 4 (Score: 10.0)", "Box 5 (Score: 10.0)",
  "Box 6 (Score: 9.0)", "Box 7 (Score: 9.0)", "Box 8 (Score: 9.0)",
  "Box 9 (Score: 9.0)", "Box 10 (Score: 9.0)", "Box 11 (Score: 9.0)",
  "Box 12 (Score: 9.0)", "Box 13 (Score: 9.0)", "Box 14 (Score: 9.0)",
  "Box 15 (Score: 9.0)", "Box 16 (Score: 9.0)", "Box 17 (Score: 9.0)",
  "Box 18 (Score: 9.0)", "Box 19 (Score: 9.0)", "Box 20 (Score: 9.0)",
  "Box 21 (Score: 9.0)", "Box 22 (Score: 9.0)", "Box 23 (Score: 9.0)",
  "Box 24 (Score: 9.0)", "Box 25 (Score: 9.0)", "Box 26 (Score: 8.0)",
  "Box 27 (Score: 8.0)", "Box 28 (Score: 8.0)", "Box 29 (Score: 8.0)",
  "Box 30 (Score: 8.0)", "Box 31 (Score: 8.0)", "Box 32 (Score: 8.0)",
  "Box 33 (Score: 8.0)", "Box 34 (Score: 8.0)", "Box 35 (Score: 8.0)",
  "Box 36 (Score: 8.0)", "Box 37 (Score: 8.0)", "Box 38 (Score: 8.0)",
  "Box 39 (Score: 8.0)", "Box 40 (Score: 8.0)", "Box 41 (Score: 8.0)",
  "Box 42 (Score: 8.0)", "Box 43 (Score: 8.0)", "Box 44 (Score: 8.0)",
  "Box 45 (Score: 8.0)", "Box 46 (Score: 8.0)", "Box 47 (Score: 8.0)",
  "Box 48 (Score: 8.0)", "Box 49 (Score: 8.0)", "Box 50 (Score: 8.0)",
  "Box 51 (Score: 8.0)", "Box 52 (Score: 8.0)", "Box 53 (Score: 8.0)",
  "Box 54 (Score: 8.0)", "Box 55 (Score: 8.0)", "Box 56 (Score: 8.0)",
  "Box 57 (Score: 8.0)", "Box 58 (Score: 8.0)", "Box 59 (Score: 8.0)",
  "Box 60 (Score: 8.0)", "Box 61 (Score: 8.0)", "Box 62 (Score: 8.0)",
  "Box 63 (Score: 8.0)", "Box 64 (Score: 8.0)", "Box 65 (Score: 7.0)",
  "Box 66 (Score: 7.0)", "Box 67 (Score: 7.0)", "Box 68 (Score: 7.0)",
  "Box 69 (Score: 7.0)", "Box 70 (Score: 7.0)", "Box 71 (Score: 7.0)",
  "Box 72 (Score: 7.0)", "Box 73 (Score: 7.0)", "Box 74 (Score: 7.0)",
  "Box 75 (Score: 7.0)", "Box 76 (Score: 7.0)", "Box 77 (Score: 7.0)",
  "Box 78 (Score: 7.0)", "Box 79 (Score: 7.0)", "Box 80 (Score: 7.0)",
  "Box 81 (Score: 7.0)", "Box 82 (Score: 7.0)", "Box 83 (Score: 7.0)",
  "Box 84 (Score: 7.0)", "Box 85 (Score: 7.0)", "Box 86 (Score: 7.0)",
  "Box 87 (Score: 7.0)", "Box 88 (Score: 7.0)", "Box 89 (Score: 7.0)",
  "Box 90 (Score: 7.0)", "Box 91 (Score: 7.0)", "Box 92 (Score: 7.0)",
  "Box 93 (Score: 7.0)", "Box 94 (Score: 7.0)", "Box 95 (Score: 7.0)",
  "Box 96 (Score: 7.0)", "Box 97 (Score: 7.0)", "Box 98 (Score: 7.0)",
  "Box 99 (Score: 7.0)", "Box 100 (Score: 7.0)", "Box 101 (Score: 7.0)",
  "Box 102 (Score: 7.0)", "Box 103 (Score: 7.0)", "Box 104 (Score: 7.0)",
  "Box 105 (Score: 7.0)", "Box 106 (Score: 7.0)", "Box 107 (Score: 7.0)",
  "Box 108 (Score: 7.0)", "Box 109 (Score: 7.0)", "Box 110 (Score: 7.0)",
  "Box 111 (Score: 7.0)", "Box 112 (Score: 7.0)", "Box 113 (Score: 7.0)",
  "Box 114 (Score: 7.0)", "Box 115 (Score: 7.0)", "Box 116 (Score: 7.0)",
  "Box 117 (Score: 7.0)", "Box 118 (Score: 7.0)", "Box 119 (Score: 7.0)",
  "Box 120 (Score: 7.0)", "Box 121 (Score: 7.0)", "Box 122 (Score: 7.0)",
  "Box 123 (Score: 7.0)", "Box 124 (Score: 7.0)", "Box 125 (Score: 7.0)",
  "Box 126 (Score: 7.0)", "Box 127 (Score: 7.0)", "Box 128 (Score: 7.0)",
  "Box 129 (Score: 7.0)", "Box 130 (Score: 7.0)", "Box 131 (Score: 7.0)",
  "Box 132 (Score: 7.0)", "Box 133 (Score: 7.0)", "Box 134 (Score: 7.0)",
  "Box 135 (Score: 7.0)", "Box 136 (Score: 7.0)", "Box 137 (Score: 7.0)",
  "Box 138 (Score: 7.0)", "Box 139 (Score: 7.0)", "Box 140 (Score: 7.0)",
  "Box 141 (Score: 7.0)", "Box 142 (Score: 7.0)", "Box 143 (Score: 7.0)",
  "Box 144 (Score: 7.0)", "Box 145 (Score: 7.0)", "Box 146 (Score: 7.0)",
  "Box 147 (Score: 7.0)", "Box 148 (Score: 7.0)", "Box 149 (Score: 7.0)",
  "Box 150 (Score: 7.0)", "Box 151 (Score: 7.0)", "Box 152 (Score: 7.0)",
  "Box 153 (Score: 7.0)", "Box 154 (Score: 7.0)", "Box 155 (Score: 7.0)",
  "Box 156 (Score: 7.0)", "Box 157 (Score: 7.0)", "Box 158 (Score: 7.0)",
  "Box 159 (Score: 7.0)", "Box 160 (Score: 7.0)", "Box 161 (Score: 7.0)",
  "Box 162 (Score: 7.0)", "Box 163 (Score: 7.0)", "Box 164 (Score: 7.0)",
  "Box 165 (Score: 7.0)", "Box 166 (Score: 7.0)", "Box 167 (Score: 7.0)",
  "Box 168 (Score: 7.0)", "Box 169 (Score: 7.0)", "Box 170 (Score: 7.0)",
  "Box 171 (Score: 7.0)", "Box 172 (Score: 7.0)", "Box 173 (Score: 7.0)",
  "Box 174 (Score: 7.0)", "Box 175 (Score: 7.0)", "Box 176 (Score: 7.0)",
  "Box 177 (Score: 7.0)", "Box 178 (Score: 7.0)", "Box 179 (Score: 7.0)",
  "Box 180 (Score: 7.0)", "Box 181 (Score: 7.0)", "Box 182 (Score: 7.0)",
  "Box 183 (Score: 7.0)", "Box 184 (Score: 6.0)", "Box 185 (Score: 6.0)",
  "Box 186 (Score: 6.0)", "Box 187 (Score: 6.0)", "Box 188 (Score: 6.0)",
  "Box 189 (Score: 6.0)", "Box 190 (Score: 6.0)", "Box 191 (Score: 6.0)",
  "Box 192 (Score: 6.0)", "Box 193 (Score: 6.0)", "Box 194 (Score: 6.0)",
  "Box 195 (Score: 6.0)", "Box 196 (Score: 6.0)", "Box 197 (Score: 6.0)",
  "Box 198 (Score: 6.0)", "Box 199 (Score: 6.0)", "Box 200 (Score: 6.0)",
  "Box 201 (Score: 6.0)", "Box 202 (Score: 6.0)", "Box 203 (Score: 6.0)",
  "Box 204 (Score: 6.0)", "Box 205 (Score: 6.0)", "Box 206 (Score: 6.0)",
  "Box 207 (Score: 6.0)", "Box 208 (Score: 6.0)", "Box 209 (Score: 6.0)",
  "Box 210 (Score: 6.0)", "Box 211 (Score: 6.0)", "Box 212 (Score: 6.0)",
  "Box 213 (Score: 6.0)", "Box 214 (Score: 6.0)", "Box 215 (Score: 6.0)",
  "Box 216 (Score: 6.0)", "Box 217 (Score: 6.0)", "Box 218 (Score: 6.0)",
  "Box 219 (Score: 6.0)", "Box 220 (Score: 6.0)", "Box 221 (Score: 6.0)",
  "Box 222 (Score: 6.0)", "Box 223 (Score: 6.0)", "Box 224 (Score: 6.0)",
  "Box 225 (Score: 6.0)", "Box 226 (Score: 6.0)", "Box 227 (Score: 6.0)",
  "Box 228 (Score: 6.0)", "Box 229 (Score: 6.0)", "Box 230 (Score: 6.0)",
  "Box 231 (Score: 6.0)", "Box 232 (Score: 6.0)", "Box 233 (Score: 6.0)",
  "Box 234 (Score: 6.0)", "Box 235 (Score: 6.0)", "Box 236 (Score: 6.0)",
  "Box 237 (Score: 6.0)", "Box 238 (Score: 6.0)", "Box 239 (Score: 6.0)",
  "Box 240 (Score: 6.0)", "Box 241 (Score: 6.0)", "Box 242 (Score: 6.0)",
  "Box 243 (Score: 6.0)", "Box 244 (Score: 6.0)", "Box 245 (Score: 6.0)",
  "Box 246 (Score: 6.0)", "Box 247 (Score: 6.0)", "Box 248 (Score: 6.0)",
  "Box 249 (Score: 6.0)", "Box 250 (Score: 6.0)", "Box 251 (Score: 6.0)",
  "Box 252 (Score: 6.0)", "Box 253 (Score: 6.0)", "Box 254 (Score: 6.0)",
  "Box 255 (Score: 6.0)", "Box 256 (Score: 6.0)", "Box 257 (Score: 6.0)",
  "Box 258 (Score: 6.0)", "Box 259 (Score: 6.0)", "Box 260 (Score: 6.0)",
  "Box 261 (Score: 6.0)", "Box 262 (Score: 6.0)", "Box 263 (Score: 6.0)",
  "Box 264 (Score: 6.0)", "Box 265 (Score: 6.0)", "Box 266 (Score: 6.0)",
  "Box 267 (Score: 6.0)", "Box 268 (Score: 6.0)", "Box 269 (Score: 6.0)",
  "Box 270 (Score: 6.0)", "Box 271 (Score: 6.0)", "Box 272 (Score: 6.0)",
  "Box 273 (Score: 6.0)", "Box 274 (Score: 6.0)", "Box 275 (Score: 6.0)",
  "Box 276 (Score: 6.0)", "Box 277 (Score: 6.0)", "Box 278 (Score: 6.0)",
  "Box 279 (Score: 6.0)", "Box 280 (Score: 6.0)", "Box 281 (Score: 6.0)",
  "Box 282 (Score: 6.0)", "Box 283 (Score: 6.0)", "Box 284 (Score: 6.0)",
  "Box 285 (Score: 6.0)", "Box 286 (Score: 6.0)", "Box 287 (Score: 6.0)",
  "Box 288 (Score: 6.0)", "Box 289 (Score: 6.0)", "Box 290 (Score: 6.0)",
  "Box 291 (Score: 6.0)", "Box 292 (Score: 6.0)", "Box 293 (Score: 6.0)",
  "Box 294 (Score: 6.0)", "Box 295 (Score: 6.0)", "Box 296 (Score: 6.0)",
  "Box 297 (Score: 6.0)", "Box 298 (Score: 6.0)", "Box 299 (Score: 6.0)",
  "Box 300 (Score: 6.0)", "Box 301 (Score: 6.0)", "Box 302 (Score: 6.0)",
  "Box 303 (Score: 6.0)", "Box 304 (Score: 6.0)", "Box 305 (Score: 6.0)",
  "Box 306 (Score: 6.0)", "Box 307 (Score: 6.0)", "Box 308 (Score: 6.0)",
  "Box 309 (Score: 6.0)", "Box 310 (Score: 6.0)", "Box 311 (Score: 6.0)",
  "Box 312 (Score: 6.0)", "Box 313 (Score: 6.0)", "Box 314 (Score: 6.0)"
  )

var box[] rects = array.new_box()
var label[] labels = array.new_label()
var bool created = false

if not created
    for i = 0 to array.size(start_times) - 1
        // 创建矩形并存储引用
        box new_rect = box.new(
          left = array.get(start_times, i),
          top = array.get(max_prices,   i),
          right = array.get(end_times,   i),
          bottom = array.get(min_prices, i),
          border_color = color.green,
          border_width = 1,
          border_style = line.style_solid,
          extend = extend.none,
          xloc = xloc.bar_time,
          bgcolor = color.new(color.green, 80)
          )
        array.push(rects, new_rect)
        // 在矩形左上角创建标签并存储引用
        label new_lbl = label.new(
          x = array.get(start_times, i),
          y = array.get(max_prices,   i),
          text = array.get(label_texts, i),
          xloc = xloc.bar_time,
          yloc = yloc.price,
          style = label.style_label_left,
          color = color.green,
          textcolor = color.white
          )
        array.push(labels, new_lbl)
    created := true