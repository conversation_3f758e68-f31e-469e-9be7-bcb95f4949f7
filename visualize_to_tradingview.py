#!/usr/bin/env python3
"""
Pine Script生成器 - 将NMS箱体检测结果转换为TradingView Pine Script
"""

import json
import os
from datetime import datetime, timezone
from pathlib import Path


def timestamp_to_pine_format(timestamp_ms):
    """将毫秒时间戳转换为Pine Script的timestamp格式"""
    dt = datetime.fromtimestamp(timestamp_ms / 1000, timezone.utc)
    return f'timestamp("UTC", {dt.year}, {dt.month}, {dt.day}, {dt.hour}, {dt.minute})'


def load_box_data(json_path):
    """加载箱体数据"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def generate_pine_arrays(boxes_data):
    """生成Pine Script数组数据"""
    boxes = boxes_data['boxes']
    
    # 提取数据
    start_times = []
    end_times = []
    min_prices = []
    max_prices = []
    label_texts = []
    
    for box in boxes:
        # 时间戳转换
        start_ts = box['technical_indicators']['time_info']['start_timestamp']
        end_ts = box['technical_indicators']['time_info']['end_timestamp']
        
        start_times.append(timestamp_to_pine_format(start_ts))
        end_times.append(timestamp_to_pine_format(end_ts))
        
        # 价格数据
        min_prices.append(str(box['boundaries']['min_price']))
        max_prices.append(str(box['boundaries']['max_price']))
        
        # 标签文本
        box_id = box['box_id']
        score = box['score']
        label_texts.append(f'"Box {box_id} (Score: {score})"')
    
    return {
        'start_times': start_times,
        'end_times': end_times,
        'min_prices': min_prices,
        'max_prices': max_prices,
        'label_texts': label_texts
    }


def format_pine_array(items, indent="  "):
    """格式化Pine Script数组，支持多行"""
    if len(items) <= 3:
        # 短数组，单行显示
        return f"array.from(\n{indent}{', '.join(items)}\n{indent})"
    else:
        # 长数组，多行显示，每行3个元素
        lines = []
        for i in range(0, len(items), 3):
            chunk = items[i:i+3]
            if i == 0:
                lines.append(f"{indent}{', '.join(chunk)},")
            elif i + 3 >= len(items):
                # 最后一行，不加逗号
                lines.append(f"{indent}{', '.join(chunk)}")
            else:
                lines.append(f"{indent}{', '.join(chunk)},")
        
        return f"array.from(\n" + "\n".join(lines) + f"\n{indent})"


def replace_placeholders(template_content, arrays_data):
    """替换模板中的占位符"""
    # 使用字符串替换的方式，更简单直接
    result = template_content

    # 替换开始时间数组
    start_times_block = '''var int[] start_times = array.from(
  timestamp("UTC", 2025, 1, 17, 4, 0) // PLACEHOLDER@start_times
  )'''
    start_times_replacement = f"var int[] start_times = {format_pine_array(arrays_data['start_times'], '  ')}"
    result = result.replace(start_times_block, start_times_replacement)

    # 替换结束时间数组
    end_times_block = '''var int[] end_times = array.from(
  timestamp("UTC", 2025, 2, 1, 20, 0) // PLACEHOLDER@end_times
  )'''
    end_times_replacement = f"var int[] end_times = {format_pine_array(arrays_data['end_times'], '  ')}"
    result = result.replace(end_times_block, end_times_replacement)

    # 替换最低价数组
    min_prices_block = '''var float[] min_prices = array.from(
  97680.0 // PLACEHOLDER@min_prices
  )'''
    min_prices_replacement = f"var float[] min_prices = {format_pine_array(arrays_data['min_prices'], '  ')}"
    result = result.replace(min_prices_block, min_prices_replacement)

    # 替换最高价数组
    max_prices_block = '''var float[] max_prices = array.from(
  110000.0 // PLACEHOLDER@max_prices
  )'''
    max_prices_replacement = f"var float[] max_prices = {format_pine_array(arrays_data['max_prices'], '  ')}"
    result = result.replace(max_prices_block, max_prices_replacement)

    # 替换标签文本数组
    label_texts_block = '''var string[] label_texts = array.from(
  "rect 1" // PLACEHOLDER@label_texts
  )'''
    label_texts_replacement = f"var string[] label_texts = {format_pine_array(arrays_data['label_texts'], '  ')}"
    result = result.replace(label_texts_block, label_texts_replacement)

    return result


def main():
    """主函数"""
    # 文件路径
    json_path = "results/BTCUSDT_4h_nms.json"
    template_path = "view.pine"
    output_path = "tmp/BTCUSDT_4h_boxes.pine"
    
    # 确保输出目录存在
    os.makedirs("tmp", exist_ok=True)
    
    print("🔄 加载箱体数据...")
    boxes_data = load_box_data(json_path)
    print(f"✅ 加载了 {len(boxes_data['boxes'])} 个箱体")
    
    print("🔄 生成Pine Script数组...")
    arrays_data = generate_pine_arrays(boxes_data)
    
    print("🔄 读取模板文件...")
    with open(template_path, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    print("🔄 替换占位符...")
    final_content = replace_placeholders(template_content, arrays_data)
    
    print("🔄 保存Pine Script文件...")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    print(f"✅ Pine Script文件已生成: {output_path}")
    print(f"📊 包含 {len(boxes_data['boxes'])} 个箱体")
    print(f"📈 数据范围: {boxes_data['metadata']['klines_count']} 根K线")
    print(f"🎯 NMS参数: IoU阈值={boxes_data['metadata']['iou_threshold']}, 最小得分={boxes_data['metadata']['min_score']}")


if __name__ == "__main__":
    main()
