#!/usr/bin/env python3
"""
Pine Script生成器 - 将NMS箱体检测结果转换为TradingView Pine Script
"""

import json
import os
import argparse
import re
from datetime import datetime, timezone
from pathlib import Path


def timestamp_to_pine_format(timestamp_ms):
    """将毫秒时间戳转换为Pine Script的timestamp格式"""
    dt = datetime.fromtimestamp(timestamp_ms / 1000, timezone.utc)
    return f'timestamp("UTC", {dt.year}, {dt.month}, {dt.day}, {dt.hour}, {dt.minute})'


def load_box_data(json_path):
    """加载箱体数据"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def generate_pine_arrays(boxes_data, label_format='Box {id} (Score: {score})'):
    """生成Pine Script数组数据"""
    boxes = boxes_data['boxes']

    # 提取数据
    start_times = []
    end_times = []
    min_prices = []
    max_prices = []
    label_texts = []

    for box in boxes:
        # 时间戳转换
        start_ts = box['technical_indicators']['time_info']['start_timestamp']
        end_ts = box['technical_indicators']['time_info']['end_timestamp']

        start_times.append(timestamp_to_pine_format(start_ts))
        end_times.append(timestamp_to_pine_format(end_ts))

        # 价格数据
        min_prices.append(str(box['boundaries']['min_price']))
        max_prices.append(str(box['boundaries']['max_price']))

        # 标签文本 - 支持自定义格式
        box_id = box['box_id']
        score = box['score']
        duration = box['technical_indicators']['basic_stats']['duration_candles']
        price_range = box['technical_indicators']['basic_stats']['price_range']

        # 格式化标签文本
        label_text = label_format.format(
            id=box_id,
            score=score,
            duration=duration,
            price_range=price_range,
            min_price=box['boundaries']['min_price'],
            max_price=box['boundaries']['max_price']
        )
        label_texts.append(f'"{label_text}"')

    return {
        'start_times': start_times,
        'end_times': end_times,
        'min_prices': min_prices,
        'max_prices': max_prices,
        'label_texts': label_texts
    }


def format_pine_array(items, indent="  "):
    """格式化Pine Script数组，支持多行"""
    if len(items) <= 3:
        # 短数组，单行显示
        return f"array.from(\n{indent}{', '.join(items)}\n{indent})"
    else:
        # 长数组，多行显示，每行3个元素
        lines = []
        for i in range(0, len(items), 3):
            chunk = items[i:i+3]
            if i == 0:
                lines.append(f"{indent}{', '.join(chunk)},")
            elif i + 3 >= len(items):
                # 最后一行，不加逗号
                lines.append(f"{indent}{', '.join(chunk)}")
            else:
                lines.append(f"{indent}{', '.join(chunk)},")
        
        return f"array.from(\n" + "\n".join(lines) + f"\n{indent})"


def replace_placeholders(template_content, arrays_data):
    """使用正则表达式替换模板中的占位符 - 只查找包含PLACEHOLDER的行进行替换"""

    # 定义替换规则：匹配从数组声明开始到结束括号的整个块
    replacements = [
        {
            'pattern': r'var int\[\] start_times = array\.from\(\s*\n.*?PLACEHOLDER@start_times.*?\n\s*\)',
            'replacement': f"var int[] start_times = {format_pine_array(arrays_data['start_times'], '  ')}"
        },
        {
            'pattern': r'var int\[\] end_times = array\.from\(\s*\n.*?PLACEHOLDER@end_times.*?\n\s*\)',
            'replacement': f"var int[] end_times = {format_pine_array(arrays_data['end_times'], '  ')}"
        },
        {
            'pattern': r'var float\[\] min_prices = array\.from\(\s*\n.*?PLACEHOLDER@min_prices.*?\n\s*\)',
            'replacement': f"var float[] min_prices = {format_pine_array(arrays_data['min_prices'], '  ')}"
        },
        {
            'pattern': r'var float\[\] max_prices = array\.from\(\s*\n.*?PLACEHOLDER@max_prices.*?\n\s*\)',
            'replacement': f"var float[] max_prices = {format_pine_array(arrays_data['max_prices'], '  ')}"
        },
        {
            'pattern': r'var string\[\] label_texts = array\.from\(\s*\n.*?PLACEHOLDER@label_texts.*?\n\s*\)',
            'replacement': f"var string[] label_texts = {format_pine_array(arrays_data['label_texts'], '  ')}"
        }
    ]

    result = template_content

    # 应用所有替换规则
    for rule in replacements:
        result = re.sub(rule['pattern'], rule['replacement'], result, flags=re.DOTALL)

    return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将NMS箱体检测结果转换为TradingView Pine Script')
    parser.add_argument('--target', required=False, help='目标名称，指定后可不必指定--input --template --output')
    parser.add_argument('--input', required=False, help='输入的NMS结果JSON文件路径')
    parser.add_argument('--template', required=False, help='Pine Script模板文件路径')
    parser.add_argument('--output', required=False, help='输出的Pine Script文件路径')
    parser.add_argument('--max-boxes', type=int, default=None, help='最大箱体数量限制 (默认: 无限制)')
    parser.add_argument('--label-format', default='Box {id} (Score: {score})',
                       help='标签格式模板 (默认: "Box {id} (Score: {score})")')
    parser.add_argument('--output-dir', default='tmp', help='输出目录 (默认: tmp)')

    args = parser.parse_args()

    # 设置默认路径
    if args.target:
        args.input = args.input or f'./results/{args.target}_nms.json'
        args.template = args.template or './view.pine'
        args.output = args.output or f'./{args.output_dir}/{args.target}_boxes.pine'
    else:
        args.input = args.input or './results/BTCUSDT_4h_nms.json'
        args.template = args.template or './view.pine'
        args.output = args.output or f'./{args.output_dir}/BTCUSDT_4h_boxes.pine'

    # 验证输入文件
    if not Path(args.input).exists():
        print(f"❌ 错误: 输入文件 {args.input} 不存在")
        return

    if not Path(args.template).exists():
        print(f"❌ 错误: 模板文件 {args.template} 不存在")
        return

    # 确保输出目录存在
    output_dir = Path(args.output).parent
    os.makedirs(output_dir, exist_ok=True)

    print(f"📁 输入文件: {args.input}")
    print(f"� 模板文件: {args.template}")
    print(f"📤 输出文件: {args.output}")
    if args.max_boxes:
        print(f"📊 最大箱体数: {args.max_boxes}")
    print()

    print("�🔄 加载箱体数据...")
    boxes_data = load_box_data(args.input)
    total_boxes = len(boxes_data['boxes'])

    # 应用箱体数量限制
    if args.max_boxes and total_boxes > args.max_boxes:
        print(f"⚠️  箱体数量 ({total_boxes}) 超过限制 ({args.max_boxes})，将截取前 {args.max_boxes} 个")
        boxes_data['boxes'] = boxes_data['boxes'][:args.max_boxes]
        total_boxes = args.max_boxes

    print(f"✅ 加载了 {total_boxes} 个箱体")

    print("🔄 生成Pine Script数组...")
    arrays_data = generate_pine_arrays(boxes_data, args.label_format)

    print("🔄 读取模板文件...")
    with open(args.template, 'r', encoding='utf-8') as f:
        template_content = f.read()

    print("🔄 替换占位符...")
    final_content = replace_placeholders(template_content, arrays_data)

    print("🔄 保存Pine Script文件...")
    with open(args.output, 'w', encoding='utf-8') as f:
        f.write(final_content)

    print(f"✅ Pine Script文件已生成: {args.output}")
    print(f"📊 包含 {total_boxes} 个箱体")
    print(f"📈 数据范围: {boxes_data['metadata']['klines_count']} 根K线")
    print(f"🎯 NMS参数: IoU阈值={boxes_data['metadata']['iou_threshold']}, 最小得分={boxes_data['metadata']['min_score']}")
    print()
    print("🚀 使用方法:")
    print("1. 复制生成的Pine Script代码")
    print("2. 在TradingView中打开Pine Editor")
    print("3. 粘贴代码并添加到图表")


if __name__ == "__main__":
    main()
