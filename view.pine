//@version=5
indicator("Multi-Rectangle List with Labels", overlay=true, max_boxes_count=500)

var int[] start_times = array.from(
  timestamp("UTC", 2025, 1, 17, 4, 0) // PLACEHOLDER@start_times
  )
var int[] end_times = array.from(
  timestamp("UTC", 2025, 2, 1, 20, 0) // PLACEHOLDER@end_times
  )
var float[] min_prices = array.from(
  97680.0 // PLACEHOLDER@min_prices
  )
var float[] max_prices = array.from(
  110000.0 // PLACEHOLDER@max_prices
  )

// 硬编码标签文本
var string[] label_texts = array.from(
  "rect 1" // PLACEHOLDER@label_texts
  )

var box[] rects = array.new_box()
var label[] labels = array.new_label()
var bool created = false

if not created
    for i = 0 to array.size(start_times) - 1
        // 创建矩形并存储引用
        box new_rect = box.new(
          left = array.get(start_times, i),
          top = array.get(max_prices,   i),
          right = array.get(end_times,   i),
          bottom = array.get(min_prices, i),
          border_color = color.green,
          border_width = 1,
          border_style = line.style_solid,
          extend = extend.none,
          xloc = xloc.bar_time,
          bgcolor = color.new(color.green, 80)
          )
        array.push(rects, new_rect)
        // 在矩形左上角创建标签并存储引用
        label new_lbl = label.new(
          x = array.get(start_times, i),
          y = array.get(max_prices,   i),
          text = array.get(label_texts, i),
          xloc = xloc.bar_time,
          yloc = yloc.price,
          style = label.style_label_left,
          color = color.green,
          textcolor = color.white
          )
        array.push(labels, new_lbl)
    created := true