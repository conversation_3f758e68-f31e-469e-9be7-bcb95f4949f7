import json
import csv
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import argparse
from pathlib import Path
import cv2
from tqdm import tqdm
from datetime import datetime, timezone


@dataclass
class BoxCandidate:
    """箱体候选类"""
    left_idx: int      # 左边界K线索引
    right_idx: int     # 右边界K线索引
    score: float       # 箱体得分
    min_price: float = 0.0   # 箱体最低价
    max_price: float = 0.0   # 箱体最高价
    
    def duration(self) -> int:
        """箱体持续时间（K线数量）"""
        return self.right_idx - self.left_idx + 1
    
    def price_range(self) -> float:
        """箱体价格范围"""
        return self.max_price - self.min_price
    
    def area(self) -> float:
        """箱体面积（时间 × 价格范围）"""
        return self.duration() * self.price_range()


class BoxNMSProcessor:
    """箱体NMS处理器"""
    
    def __init__(self, iou_threshold: float = 0.5, min_score: float = 0.0):
        """
        初始化处理器
        
        Args:
            iou_threshold: IoU阈值，超过此值的箱体将被抑制
            min_score: 最小得分阈值，低于此值的箱体将被过滤
        """
        self.iou_threshold = iou_threshold
        self.min_score = min_score
    
    def load_kline_data(self, json_path: str) -> List[Dict]:
        """加载K线数据"""
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert to structured format
        klines = []
        for item in data:
            klines.append({
                'timestamp': int(item[0]),
                'open': float(item[1]),
                'high': float(item[2]),
                'low': float(item[3]),
                'close': float(item[4]),
                'volume': float(item[5])
            })
        return klines
    
    def load_scores_matrix(self, csv_path: str) -> np.ndarray:
        """加载得分矩阵"""
        scores = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                # Convert empty strings to 0, others to float
                score_row = []
                for cell in row:
                    if cell.strip() == '':
                        score_row.append(0.0)
                    else:
                        try:
                            score_row.append(float(cell))
                        except ValueError:
                            score_row.append(0.0)
                scores.append(score_row)
        return np.array(scores)
    
    def extract_candidates(self, scores_matrix: np.ndarray) -> List[BoxCandidate]:
        """从得分矩阵提取候选箱体（优化版本）"""
        print(f"Extracting candidates with min_score >= {self.min_score}...")

        # Use numpy vectorized operations for efficiency
        valid_indices = np.where(scores_matrix > self.min_score)
        valid_scores = scores_matrix[valid_indices]

        candidates = []
        print(f"Found {len(valid_scores)} valid candidates, creating objects...")

        # Create candidates with progress bar
        for idx in tqdm(range(len(valid_scores)), desc="Creating candidates"):
            i, j = valid_indices[0][idx], valid_indices[1][idx]
            score = valid_scores[idx]

            # Basic validation: right_idx should be >= left_idx
            if j >= i:
                candidates.append(BoxCandidate(
                    left_idx=i,
                    right_idx=j,
                    score=score
                ))

        print(f"Created {len(candidates)} valid candidates")
        return candidates
    
    def calculate_box_bounds_batch(self, candidates: List[BoxCandidate], klines: List[Dict]) -> List[BoxCandidate]:
        """批量计算箱体的价格边界（优化版本）"""
        print("Calculating box bounds...")

        # Pre-extract price arrays for efficiency
        highs = np.array([k['high'] for k in klines])
        lows = np.array([k['low'] for k in klines])
        klines_count = len(klines)

        valid_candidates = []

        for candidate in tqdm(candidates, desc="Computing bounds"):
            # Validate indices
            if (candidate.right_idx >= klines_count or
                candidate.left_idx < 0 or
                candidate.left_idx > candidate.right_idx):
                continue

            # Use numpy slicing for efficiency
            start_idx = candidate.left_idx
            end_idx = candidate.right_idx + 1

            box_highs = highs[start_idx:end_idx]
            box_lows = lows[start_idx:end_idx]

            if len(box_highs) == 0:
                continue

            candidate.min_price = float(np.min(box_lows))
            candidate.max_price = float(np.max(box_highs))

            # Skip boxes with zero price range
            if candidate.price_range() > 0:
                valid_candidates.append(candidate)

        print(f"Computed bounds for {len(valid_candidates)} valid candidates")
        return valid_candidates
    
    def nms(self, candidates: List[BoxCandidate]) -> List[BoxCandidate]:
        """
        使用OpenCV的高性能NMS实现
        """
        if not candidates:
            return []

        print(f"Applying NMS to {len(candidates)} candidates...")

        # Convert candidates to OpenCV format
        boxes = []
        scores = []

        for candidate in candidates:
            # Convert to [x, y, width, height] format for OpenCV
            # x = left_idx, y = min_price, width = duration, height = price_range
            x = float(candidate.left_idx)
            y = float(candidate.min_price)
            width = float(candidate.duration())
            height = float(candidate.price_range())

            # Skip invalid boxes
            if width <= 0 or height <= 0:
                continue

            boxes.append([x, y, width, height])
            scores.append(float(candidate.score))

        if not boxes:
            return []

        # Convert to numpy arrays
        boxes = np.array(boxes, dtype=np.float32)
        scores = np.array(scores, dtype=np.float32)

        print(f"Running OpenCV NMS with {len(boxes)} valid boxes...")

        # Apply NMS using OpenCV
        indices = cv2.dnn.NMSBoxes(
            boxes.tolist(),
            scores.tolist(),
            score_threshold=0.0,  # We already filtered by min_score
            nms_threshold=self.iou_threshold
        )

        # Extract selected candidates
        selected = []
        if len(indices) > 0:
            # Handle both old and new OpenCV versions
            if isinstance(indices, tuple):
                indices = indices[0]

            indices = indices.flatten() if hasattr(indices, 'flatten') else indices

            for idx in indices:
                if isinstance(idx, (list, np.ndarray)):
                    idx = idx[0]
                selected.append(candidates[int(idx)])

        print(f"NMS selected {len(selected)} boxes")
        return selected

    def _convert_to_json_serializable(self, obj):
        """将numpy类型转换为JSON可序列化的类型"""
        if isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def calculate_technical_indicators(self, box: BoxCandidate, klines: List[Dict]) -> Dict:
        """
        计算箱体的技术分析指标
        """
        if box.right_idx >= len(klines) or box.left_idx < 0:
            return {}

        # Extract box data
        box_klines = klines[box.left_idx:box.right_idx + 1]

        if not box_klines:
            return {}

        # Basic statistics
        opens = [k['open'] for k in box_klines]
        highs = [k['high'] for k in box_klines]
        lows = [k['low'] for k in box_klines]
        closes = [k['close'] for k in box_klines]
        volumes = [k['volume'] for k in box_klines]

        # Price statistics
        price_range = box.price_range()
        price_center = (box.max_price + box.min_price) / 2

        # Volume statistics
        total_volume = sum(volumes)
        avg_volume = total_volume / len(volumes) if volumes else 0
        volume_std = np.std(volumes) if len(volumes) > 1 else 0

        # Price volatility (standard deviation of close prices)
        price_volatility = np.std(closes) if len(closes) > 1 else 0

        # Price change within box
        price_change = closes[-1] - closes[0] if len(closes) >= 2 else 0
        price_change_pct = (price_change / closes[0] * 100) if closes[0] != 0 else 0

        # Box strength indicators
        touches_upper = sum(1 for h in highs if abs(h - box.max_price) / box.max_price < 0.01)
        touches_lower = sum(1 for l in lows if abs(l - box.min_price) / box.min_price < 0.01)

        # Time-based indicators
        duration_hours = box.duration() * 4  # Assuming 4-hour candles

        # Time information
        start_timestamp = klines[box.left_idx]['timestamp'] if box.left_idx < len(klines) else None
        end_timestamp = klines[box.right_idx]['timestamp'] if box.right_idx < len(klines) else None

        # Convert timestamps to readable format
        if start_timestamp is not None and end_timestamp is not None:
            start_time_str = datetime.fromtimestamp(start_timestamp / 1000, timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
            end_time_str = datetime.fromtimestamp(end_timestamp / 1000, timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
        else:
            start_time_str = None
            end_time_str = None

        return {
            'time_info': {
                'start_timestamp': start_timestamp,
                'end_timestamp': end_timestamp,
                'start_time': start_time_str,
                'end_time': end_time_str,
                'duration_ms': end_timestamp - start_timestamp if (start_timestamp and end_timestamp) else None
            },
            'basic_stats': {
                'duration_candles': box.duration(),
                'duration_hours': duration_hours,
                'price_range': price_range,
                'price_center': price_center,
                'area': box.area()
            },
            'price_stats': {
                'min_price': box.min_price,
                'max_price': box.max_price,
                'open_price': opens[0] if opens else 0,
                'close_price': closes[-1] if closes else 0,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'price_volatility': price_volatility
            },
            'volume_stats': {
                'total_volume': total_volume,
                'avg_volume': avg_volume,
                'volume_std': volume_std,
                'max_volume': max(volumes) if volumes else 0,
                'min_volume': min(volumes) if volumes else 0
            },
            'strength_indicators': {
                'upper_touches': touches_upper,
                'lower_touches': touches_lower,
                'touch_ratio': (touches_upper + touches_lower) / len(box_klines) if box_klines else 0
            }
        }

    def process(self, scores_csv_path: str, klines_json_path: str, output_path: str) -> Dict:
        """
        执行完整的处理流程
        """
        print("Loading data...")
        klines = self.load_kline_data(klines_json_path)
        scores_matrix = self.load_scores_matrix(scores_csv_path)

        print(f"Loaded {len(klines)} K-lines and {scores_matrix.shape} scores matrix")

        print("Extracting candidates...")
        candidates = self.extract_candidates(scores_matrix)
        print(f"Found {len(candidates)} initial candidates")

        # Calculate box bounds (this also filters invalid candidates)
        candidates = self.calculate_box_bounds_batch(candidates, klines)

        print("Applying NMS...")
        selected_boxes = self.nms(candidates)
        print(f"Selected {len(selected_boxes)} boxes after NMS")

        print("Calculating technical indicators...")
        results = []
        for i, box in enumerate(selected_boxes):
            indicators = self.calculate_technical_indicators(box, klines)

            result = {
                'box_id': int(i),
                'boundaries': {
                    'left_idx': int(box.left_idx),
                    'right_idx': int(box.right_idx),
                    'min_price': float(box.min_price),
                    'max_price': float(box.max_price)
                },
                'score': float(box.score),
                'technical_indicators': self._convert_to_json_serializable(indicators)
            }
            results.append(result)

        # Prepare output
        output_data = {
            'metadata': {
                'total_candidates': len(candidates),
                'selected_boxes': len(selected_boxes),
                'iou_threshold': self.iou_threshold,
                'min_score': self.min_score,
                'klines_count': len(klines)
            },
            'boxes': results
        }

        # Save results
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"Results saved to {output_path}")
        return output_data


def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='K线箱体检测结果NMS后处理')
    parser.add_argument('--target', required=True, help='目标名称，指定后可不必指定--scores --klines --output')
    parser.add_argument('--scores', required=False, help='得分CSV文件路径')
    parser.add_argument('--klines', required=False, help='K线JSON文件路径')
    parser.add_argument('--output', required=False, help='输出JSON文件路径')
    parser.add_argument('--iou-threshold', type=float, default=0.5, help='IoU阈值 (默认: 0.5)')
    parser.add_argument('--min-score', type=float, default=0.0, help='最小得分阈值 (默认: 0.0)')

    args = parser.parse_args()
    args.scores = args.scores or f'./results/{args.target}.csv'
    args.klines = args.klines or f'./data/{args.target}.json'
    args.output = args.output or f'./results/{args.target}_nms.json'

    # Validate input files
    if not Path(args.scores).exists():
        print(f"Error: Scores file {args.scores} not found")
        return

    if not Path(args.klines).exists():
        print(f"Error: K-lines file {args.klines} not found")
        return

    # Create processor
    processor = BoxNMSProcessor(
        iou_threshold=args.iou_threshold,
        min_score=args.min_score
    )

    # Process
    try:
        results = processor.process(args.scores, args.klines, args.output)
        print(f"\nProcessing completed successfully!")
        print(f"Total boxes found: {results['metadata']['selected_boxes']}")
    except Exception as e:
        print(f"Error during processing: {e}")
        raise


if __name__ == "__main__":
    main()
